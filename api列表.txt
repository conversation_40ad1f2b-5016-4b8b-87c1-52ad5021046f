# 航線商城 API 列表

## 基本配置
- 開發環境後端地址：http://************:8080
- 生產環境：使用相對路徑，通過 Nginx 代理到後端
- 所有API都使用 `/api/` 前綴

## Mixed Content 問題解決方案
- ✅ 問題：HTTPS 前端無法連接 HTTP 後端 (Mixed Content 錯誤)
- ✅ 解決：修改 vite.config.js，生產環境使用空字串（相對路徑）
- ✅ 配置：前端在生產環境中通過 nginx 代理連接後端，避免直接 HTTP 連接
- ✅ WebSocket：生產環境通過 nginx 代理 /socket.io/ 路徑到後端
- ✅ nginx 配置：已更新支援 /api/, /store/, /uploads/, /socket.io/, /shipping-addresses/, /active-promotions/ 路由

## 修改記錄
- ✅ 後端配置已修改：開發環境使用 http://************:8080，生產環境使用相對路徑通過 Nginx 代理
- ✅ Mixed Content 問題已解決：前端生產環境配置為使用相對路徑代理
- ✅ 所有前台API調用已統一使用 `/api/` 前綴
- ✅ 所有後台API調用已統一使用 `/api/admin/` 前綴
- ✅ 前端代理配置已更新，移除不必要的 `/admin` 代理
- ✅ 修正遺漏的API調用：Header.vue, UpdateProfileView.vue, DeliveryDatePicker.vue, MemberProfileView.vue, MyOrdersView.vue, ResetPasswordView.vue, ProductDetailView.vue, CheckoutView.vue (多個API)
- ✅ 更新Nginx配置文件，移除舊的API路由規則，統一使用 /api/ 前綴
- ✅ 新增優惠活動同步腳本：backend/scripts/sync-promotions.js
- ✅ 前端配置增加 useLocalBackend 控制開關：支援本地/遠端後端切換
- ✅ WebSocket 服務優化：支援訪客模式連接和多種通知事件
- ✅ 算錢工具修改：排除月結用戶的自動新增訂單功能，修正店家顯示名稱使用用戶自訂名稱
- ✅ 商品比價管理優化：左右分開顯示本站商品和TheOne商品，各自支援搜尋和分類篩選
- ✅ 紅利點數發放增加會員狀態檢查：只有狀態正常的會員才能獲得紅利點數
- ✅ 優化順豐運單列印彈窗：新增真正的取消按鈕，改善合併確認對話框用戶體驗，支援響應式設計
  - 合併確認對話框：新增「取消」、「分別創建運單」、「確認合併並創建運單」三個選項
  - 所有確認對話框：統一樣式，支援桌面版和手機版響應式設計
  - 按鈕設計：符合Ant Design規範，具備最小寬度確保觸控友好
- ✅ 修正今日銷售統計計算邏輯：
  - 改為基於訂單建立時間（created_at）而非完成出貨時間
  - 排除已取消的訂單（狀態ID 6和7）和退貨訂單（狀態ID 8、9、10）
  - 修正時區處理問題，使用UTC時間比較確保準確性
  - 包含所有非取消且非退貨狀態的訂單，不只是完成出貨的訂單
  - 同時修正本月銷售統計的計算邏輯，保持一致性
- ✅ 修正本月銷售統計計算邏輯：
  - 排除匯入的歷史訂單資料，只計算真正本月建立的訂單
  - 排除已取消的訂單（狀態ID 6和7）和退貨訂單（狀態ID 8、9、10）
  - 使用訂單編號格式篩選（202506%），確保只計算本月建立的訂單
  - 解決因匯入歷史訂單導致本月銷售金額異常偏高的問題
- ✅ 修正銷售額和業績計算邏輯：
  - 儀表板總銷售額計算：排除狀態為「退貨」（ID 8、9、10）或「已取消」（ID 6、7）的商品金額
  - 所有業績統計功能：包括月度業績、年度業績、商品銷售排行等，都排除退貨和取消的商品
  - 商品銷售量計算：排除退貨和取消訂單的商品，確保銷售排行準確性
  - 前端顯示和後端計算邏輯保持一致
- ✅ 後台權限設定修改：
  - 將 "hao" 帳號權限從 'staff' 提升為 'admin'，使其能夠訪問商品比價頁面
- ✅ 修正後台商品圖片上傳檔案命名邏輯：
  - 移除自動添加的 CGP0001_ 前綴，改為直接使用 SKU 作為檔案名稱
  - 修正 backend/routes/admin_routes/products.js 和 backend/routes/productRoutes.js 中的 generateCGPFilename 函數
  - 創建清理腳本 backend/scripts/remove_cgp_prefix.js 清除資料庫中現有的 CGP 前綴
  - 新的檔案命名格式：{SKU}.jpg（例如：SAM_SCR_S00_122.jpg）
  - 已清理 66 筆包含 CGP 前綴的圖片記錄，資料庫中不再有 CGP 前綴的圖片路徑
  - 修改初始化腳本：backend/scripts/init_admins.js，支援 role 欄位設定
  - 更新管理員API：backend/routes/admin_routes/admins.js，支援 role 欄位查詢和更新
  - 新增管理員權限更新API：PUT /api/admin/admins/:id/role
  - 確保商品比價頁面的權限控制機制正確運作（後端 requireSuperAdmin 中間件 + 前端 requiresAdmin 路由守衛）
- ✅ 手動開發票功能優化：
  - 修改二聯式發票驗證邏輯：二聯式發票不需要統一編號
  - 後端API修改：backend/routes/admin_routes/invoices.js，二聯式發票時統一編號設為空字串
  - 前端驗證修改：admin/src/views/ManualInvoiceView.vue，二聯式發票統一編號欄位非必填
  - 前端UI優化：二聯式發票時統一編號欄位完全隱藏，買方名稱欄位自動擴展為全寬
  - 三聯式發票佈局調整：統一編號在左邊，公司抬頭在右邊
  - 預覽功能優化：二聯式發票預覽時統一編號顯示為「不適用」
  - 新增API記錄：POST /api/admin/invoices/manual 手動開發票API
  - 新增常用客戶功能：
    - GET /api/admin/invoices/frequent-customers 獲取常用客戶列表
    - POST /api/admin/invoices/frequent-customers 新增常用客戶
    - DELETE /api/admin/invoices/frequent-customers/:id 刪除常用客戶
- ✅ 退貨申請處理功能優化：
  - 支援個別商品項目處理：將數量大於1的退貨商品拆分成多個獨立項目
  - 前端介面優化：每個商品項目可單獨選擇通過、拒絕或送大陸判定
  - 移除數量控制輸入框，改為每個項目代表1個商品
  - 支援對同一個商品的不同項目進行不同的處理（如：退貨2個鍵盤，可以通過1個拒絕1個）
  - 新增送大陸判定選項：不進行退款操作，只記錄狀態
  - 自動計算每個項目的紅利點數（移除退款金額顯示，只退紅利）
  - 後端API支援：processedQuantity 參數記錄實際通過的數量
  - 資料庫新增欄位：processed_quantity, actual_refund_amount, actual_refund_points, mainland_china_status, mainland_china_confirmed_at
  - 退貨總表新增送大陸判定統計欄位
  - 修正Vue模板語法：移除JSX語法，改用Vue模板語法顯示送大陸判定狀態
  - 介面簡化：移除退款金額顯示，只顯示退還紅利點數

## 紅利點數發放規則
- 只有會員狀態為「正常」（review_status = 1 且 is_active = 1）的用戶才能獲得紅利點數
- 此規則適用於：
  - 訂單完成出貨流程時的紅利回饋發放（transaction_type = 'no_invoice_reward'）
  - 管理員手動發放的紅利點數（transaction_type = 'admin_add'）
- 紅利統計API也只統計狀態正常會員的紅利發放記錄
- 紅利點數返還和扣除操作不受會員狀態限制（用於訂單取消、退貨等回滾操作）

## 前台用戶 API

### 認證相關
- POST /api/register - 用戶註冊
- POST /api/login - 用戶登入
- POST /api/forgot-password - 忘記密碼
- POST /api/reset-password - 重設密碼

### 用戶資料
- GET /api/cities - 獲取城市列表
- GET /api/users/:username/profile - 獲取用戶資料
- PUT /api/users/:username/update - 更新用戶資料
- GET /api/user/invoice-info - 獲取用戶發票資料
- PUT /api/user/invoice-info - 更新用戶發票資料

### 商品相關
- GET /api/products - 獲取商品列表
  - 參數：page, limit, category, sort, order, search
  - sort 支援：hot_sales（熱賣商品，過去7天銷售量排序）, created_at（最新商品）, retail_price（零售價）, sort_order（自訂排序）
  - 預設排序：無分類時使用 hot_sales，有分類時使用 sort_order
  - 回傳資料包含：sales_volume_7days（7天銷售量）
  - 銷量計算：排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單
- GET /api/products/:id - 獲取商品詳情
- POST /api/products - 新增商品（需要上傳權限）
- GET /api/categories - 獲取商品分類

### 購物車
- GET /api/cart - 獲取購物車
- POST /api/cart/add - 添加商品到購物車
- PUT /api/cart/update - 更新購物車商品數量
- DELETE /api/cart/remove - 移除購物車商品
- DELETE /api/cart/clear - 清空購物車
- POST /api/cart/validate-stock - 驗證庫存
- POST /api/cart/restore-stock - 回補庫存

### 訂單管理
- POST /api/orders/create - 創建訂單
- GET /api/orders/today-cumulative-amount - 獲取當天累積金額
- GET /api/orders/user-orders - 獲取用戶訂單列表
- GET /api/orders/:orderId - 獲取訂單詳情
- PUT /api/orders/:orderId/cancel - 取消訂單
- GET /api/orders/cancelled/list - 獲取已取消訂單

### 收貨地址
- GET /api/shipping-addresses - 獲取收貨地址列表
- POST /api/shipping-addresses - 新增收貨地址
- PUT /api/shipping-addresses/:id - 更新收貨地址
- DELETE /api/shipping-addresses/:id - 刪除收貨地址
- PUT /api/shipping-addresses/:id/default - 設為預設地址

### 紅利點數
- GET /api/user/bonus-points - 獲取用戶紅利點數

### 發票相關 (已切換至正式環境)
- GET /api/invoices/:orderId - 獲取訂單發票
- POST /api/invoices/generate - 生成發票 (0元訂單自動跳過)
- 發票API使用 Amego 正式環境
- 公司統編：60775684 (航線國際有限公司)
- API網址：https://invoice-api.amego.tw
- 0元訂單自動跳過發票開立，並記錄到 skipped_invoice_records 表

### 產品代尋
- POST /api/product-requests - 提交產品代尋請求
- GET /api/my-product-requests - 獲取我的產品代尋記錄
- GET /api/my-product-requests/:id - 獲取產品代尋詳情

### 公告
- GET /api/announcement/current - 獲取當前有效公告
- POST /api/announcement/mark-read - 標記公告為已讀

### 退貨申請
- POST /api/return-requests - 提交退貨申請
  - 申請限制：已申請過退貨的商品不能再次申請，除非是後台拒絕退貨的商品（status = 'rejected'）
  - 檢查狀態：pending（待處理）、processing（處理中）、processed（已完成）的申請會阻止再次申請
  - 只有 rejected（已拒絕）狀態的申請允許重新申請退貨
- GET /api/return-requests - 獲取退貨申請列表
- GET /api/return-requests/:id - 獲取退貨申請詳情
  - 只能查看屬於當前用戶的退貨申請
  - 回傳資料包含：退貨申請基本資訊、退貨商品詳情列表
  - 商品詳情包含：商品資訊、處理狀態、數量資訊、退款資訊、管理員備註
- 功能優化：支援部分數量退貨處理
  - 管理員可對同一筆退貨申請中的不同商品進行個別處理
  - 支援對同一個商品的不同數量進行不同的處理（如：申請退貨3個，只通過2個）
  - 新增處理數量控制：processedQuantity 參數
  - 新增實際退款金額和紅利點數記錄：actual_refund_amount, actual_refund_points
  - 資料庫新增欄位：processed_quantity, actual_refund_amount, actual_refund_points
  - 前端介面優化：顯示原購買數量、申請退貨數量、實際處理數量
  - 支援即時計算退款金額和紅利點數

### 商店狀態
- GET /api/store/status - 獲取商店營業狀態
- GET /api/store/pickup-shipping-status - 檢查取貨寄送狀態

### 產品搜尋
- GET /api/product-search - 搜尋商品
  - 參數：search, category, page, limit, sort, order
  - sort 支援：hot_sales（熱賣商品，過去7天銷售量排序）, created_at（最新商品）, retail_price（零售價）, sort_order（自訂排序）
  - 預設排序：無分類時使用 hot_sales，有分類時使用 sort_order
  - 回傳資料包含：sales_volume_7days（7天銷售量）
  - 銷量計算：排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單

### 意見反饋
- POST /api/feedback/submit - 提交意見反饋

### 公開內容
- GET /api/public/about-us - 關於我們
- GET /api/public/warranty-shipping - 保固與運費

## 後台管理 API

### 管理員認證
- POST /api/admin/login - 管理員登入

### 用戶管理
- GET /api/admin/users - 獲取用戶列表
  - 參數：page, limit, search, memberGroup, status, sortField, sortOrder
  - sortField 支援：admin_name（自訂名稱）, full_name（會員名字）, username（會員帳號）, email（電子郵件）, member_group（會員群組）, review_status（審核狀態）, created_at（加入日期）, total_bonus_points（紅利點數）
  - sortOrder 支援：asc（升序）, desc（降序）
  - 支援伺服器端排序，排序後自動重置到第一頁
- GET /api/admin/users/search - 會員搜尋接口（用於下拉選單）
  - 參數：search（搜尋關鍵字，可為空）, limit（限制數量，預設50）
  - 回傳：會員列表，包含 id, label（顯示名稱）, value（會員ID）, admin_name, full_name, username, email
  - 只搜尋已審核通過且啟用的會員
  - 當 search 為空時，返回所有會員按 A-Z 排序；有搜尋關鍵字時進行模糊搜尋
- GET /api/admin/users/stats - 獲取會員統計數據
  - 回傳：total_users（總會員數）, approved_users（已通過審核）, pending_users（待審核）, rejected_users（已拒絕）, active_users（啟用中）, inactive_users（已停用）
- GET /api/admin/users/bonus-stats - 獲取紅利發放統計數據（只統計狀態正常會員的紅利發放）
  - 回傳：total_bonus_issued（總紅利發放）, order_bonus_reward（訂單紅利回饋）, admin_bonus_add（管理員發放）, total_bonus_transactions（紅利交易次數）
  - 註：只統計 review_status = 1 且 is_active = 1 的會員紅利發放記錄
- GET /api/admin/users/:id - 獲取用戶詳情
- POST /api/admin/users - 新增用戶
- PUT /api/admin/users/:id - 更新用戶
- DELETE /api/admin/users/:id - 刪除用戶

### 商品管理
- GET /api/admin/products - 獲取商品列表（支援銷量排序和銷量等級）
  - 參數：page, limit, search, categoryIds, sortField, sortOrder, isActive
  - sortField 支援：sales_volume（銷量）, name, sku, retail_price, stock_quantity, created_at
  - 預設排序：按創建時間降序（created_at DESC），最新上架的商品顯示在最上方
  - 回傳資料包含：weekly_sales_volume（7天銷量，用於計算銷量等級）
  - 銷量計算：排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單
- GET /api/admin/products/stats - 獲取商品統計
- GET /api/admin/products/:id - 獲取商品詳情
- POST /api/admin/products - 新增商品
- PUT /api/admin/products/:id - 更新商品
- DELETE /api/admin/products/:id - 刪除商品
- GET /api/admin/products/:id/specs - 獲取商品規格
- GET /api/admin/products/:id/categories - 獲取商品分類
- GET /api/admin/products/:id/prices - 獲取商品價格
- GET /api/admin/products/:id/images - 獲取商品圖片
- DELETE /api/admin/products/:id/images/:imageId - 刪除商品圖片
- PUT /api/admin/products/:id/images/:imageId/primary - 設定主圖
- POST /api/admin/products/:id/copy - 複製商品
- PUT /api/admin/products/:id/sort - 更新商品排序
- GET /api/admin/products/sku/:sku - 根據SKU查詢商品

### 分類管理
- GET /api/admin/categories - 獲取分類列表
- POST /api/admin/categories - 新增分類
- PUT /api/admin/categories/:id - 更新分類
- DELETE /api/admin/categories/:id - 刪除分類

### 庫存管理
- GET /api/admin/inventory - 獲取庫存列表
- PUT /api/admin/inventory/:id - 更新庫存
- GET /api/admin/inventory/movements - 獲取庫存異動記錄

### 盤點管理
- GET /api/admin/inventory-count - 獲取盤點記錄
- GET /api/admin/inventory-count/uncounted-products - 獲取未盤點商品清單
- GET /api/admin/inventory-count/:id - 獲取盤點詳情
- POST /api/admin/inventory-count - 新增盤點
- PUT /api/admin/inventory-count/:id - 更新盤點（包含項目）
- PATCH /api/admin/inventory-count/:id/status - 更新盤點狀態
- POST /api/admin/inventory-count/:id/complete - 完成盤點（更新庫存並記錄異動）
- DELETE /api/admin/inventory-count/:id - 刪除盤點（已完成的盤點會自動還原庫存）

### 進貨管理
- GET /api/admin/purchase - 獲取進貨記錄
- POST /api/admin/purchase - 新增進貨記錄

### 進貨單管理
- GET /api/admin/purchase-orders - 獲取進貨單列表
- POST /api/admin/purchase-orders - 新增進貨單

### 廠商管理
- GET /api/admin/suppliers - 獲取廠商列表
- POST /api/admin/suppliers - 新增廠商
- PUT /api/admin/suppliers/:id - 更新廠商
- DELETE /api/admin/suppliers/:id - 刪除廠商

### 訂單管理
- GET /api/admin/orders - 獲取訂單列表
  - 參數：page, limit, status, paymentStatus, shippingMethod, startDate, endDate, search, userIds, includeItems, noPagination, excludeCancelled
  - userIds：會員ID篩選，多個ID用逗號分隔
  - excludeCancelled：是否排除已取消的訂單（true/false）
  - 回傳資料包含：hasReturn（是否有退貨記錄）, returnStatus（退貨狀態）, returnStatusText（退貨狀態文字）
- GET /api/admin/orders/status-counts - 獲取訂單狀態統計
- GET /api/admin/orders/:id - 獲取訂單詳情
- PUT /api/admin/orders/:id/status - 更新訂單狀態
- PUT /api/admin/orders/:id/payment-status - 更新付款狀態
  - 支援的付款狀態值：0(未付款), 1(已付款), 2(取貨未付款), 3(客戶已付款), 4(大哥取貨未付款), 5(已匯款), 6(已核對)
  - 已付款(1)、已匯款(5)和已核對(6)狀態可點擊選擇「已核對」或「清除狀態」
  - 已核對狀態只顯示「還原為已付款」選項
  - 清除狀態邏輯：已核對→已付款，其他狀態→未付款
- PUT /api/admin/orders/:id/shipping-method - 更新送貨方式
- PUT /api/admin/orders/:id/items/:itemId - 更新訂單項目
- PUT /api/admin/orders/batch-status-update - 批量更新訂單狀態
- PUT /api/admin/orders/payment-status - 批量更新付款狀態
- PUT /api/admin/orders/update-by-shipping - 根據送貨方式批量更新訂單狀態
- PUT /api/admin/orders/complete-shipping - 完成出貨流程
- PUT /api/admin/orders/cancel-orders - 取消訂單
- POST /api/admin/orders/print-shipping-details - 列印出貨明細
- POST /api/admin/orders/print-sf-express-shipping-label - 列印順豐運單

- GET /api/admin/orders/check-unpaid/:userId - 檢查用戶未付款訂單
- POST /api/admin/orders/verification/scan - 驗證商品條碼
- GET /api/admin/orders/stats/daily-sales - 獲取今日銷售統計
  - 排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單
  - 基於訂單購買日期計算，使用GMT+8時間比較
- GET /api/admin/orders/stats/monthly-sales - 獲取本月銷售統計
  - 排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單
  - 只計算本月建立的訂單（根據訂單編號格式篩選）
- GET /api/admin/orders/stats/monthly-order-stats - 獲取指定月份訂單統計
  - 參數：year（年份）, month（月份）
  - 回傳：總訂單數、現金流總金額、匯款總金額
- GET /api/admin/orders/stats/sales-range - 獲取自訂日期範圍銷售統計
  - 參數：startDate（開始日期）, endDate（結束日期）
  - 回傳：統計摘要（總訂單數、已完成訂單數、總銷售額、紅利折抵、實際收入、匯款總金額、平均訂單金額）
  - 排除已取消（狀態ID 6、7）和退貨（狀態ID 8、9、10）的訂單

### 退貨申請管理
- GET /api/admin/return-requests - 獲取退貨申請列表
- GET /api/admin/return-requests/mainland-china - 獲取送大陸判定列表
  - 只顯示包含送大陸判定項目的退貨申請
  - 支援分頁查詢
  - 回傳資料包含：pending_confirmation_count（待確認數量）, confirmed_count（已確認數量）
  - 送大陸判定狀態：'sent'（已送大陸，待確認）, 'confirmed'（已確認退款）
- GET /api/admin/return-requests/:id - 獲取退貨申請詳情
- PUT /api/admin/return-requests/:id/process - 處理退貨申請
  - 支援部分數量退貨處理
  - 請求參數：action (approve/reject/mainland_china), adminNotes, items[]
  - items 參數：itemId, action (approve/reject/mainland_china), adminNotes, processedQuantity, mainlandChinaQuantity
  - processedQuantity：實際處理的退貨數量（可小於申請數量）
  - mainlandChinaQuantity：送大陸判定的數量
  - 支援對同一個商品的不同數量進行不同的處理
  - 自動計算實際退款金額和紅利點數
  - 支援不開發票紅利回饋的按比例扣除
  - 通過的商品：立即退還紅利點數給用戶（先補償）
  - 送大陸判定：不進行退款操作，只記錄狀態，等待後續確認
- PUT /api/admin/return-requests/:id/confirm-mainland-refund - 確認送大陸判定退款
  - 請求參數：itemIds（要確認退款的項目ID陣列）, adminNotes（管理員備註）
  - 功能：收到大陸通知後確認退款，將項目狀態從 'sent' 更新為 'confirmed'
  - 自動計算並退還紅利點數給客戶
  - 記錄確認時間戳（mainland_china_confirmed_at）
  - 支援批量確認多個項目
  - 回傳：totalRefundPoints（總退還紅利點數）, confirmedItems（已確認項目列表）
  - 紅利說明內容：大陸確認退款 - 商品名稱（原為「大陸確認退款 - 退貨申請 #編號」）
  - 紅利交易類型：return_refund（原為mainland_china_refund，現統一為退貨返還）

### 成本計算器
- POST /api/admin/cost/calculate - 計算成本
- GET /api/admin/cost/dailyTotal/:date - 獲取指定日期的總金額
- GET /api/admin/cost/dailyTotal/:date?checkRecord=true - 獲取指定日期的總金額並檢查是否有記錄
- POST /api/admin/cost/dailyTotal - 保存每日總結

### 順豐快遞
- POST /api/admin/sf-express/create-waybill - 創建運單
- POST /api/admin/sf-express/batch-create - 批量創建運單（支援併單功能）
  - 參數：orderIds（訂單ID陣列）, isTest（是否測試環境）, enableMerge（是否啟用併單）
  - 併單規則：同收件人（姓名+電話+地址）的訂單自動合併
  - 收費標準（基於扣除紅利折抵後的實際金額）：
    - 月結用戶：實際金額≤$1500只收運費$100，>$1500完全免費
    - 一般用戶：實際金額<$1500收取實際金額+$100運費，≥$1500免運費只收實際金額
- POST /api/admin/sf-express/detect-merge - 檢測可併單的訂單
- GET /api/admin/sf-express/query/:waybillNo - 查詢運單狀態
- POST /api/admin/sf-express/print - 列印運單（收方付款，上門收件）
- POST /api/admin/sf-express/cancel - 取消運單
- GET /api/admin/sf-express/order/:orderId/waybill - 獲取訂單的運單記錄
- POST /api/admin/sf-express/test - 測試順豐 API 連接

### 系統設定
- GET /api/admin/settings - 獲取系統設定
- PUT /api/admin/settings - 更新系統設定

### 網站內容設定
- GET /api/admin/settings/about - 獲取關於我們設定
- PUT /api/admin/settings/about - 更新關於我們設定
- GET /api/admin/settings/warranty - 獲取保固與運費設定
- PUT /api/admin/settings/warranty - 更新保固與運費設定

### 商店營業時間
- GET /api/admin/settings/store-hours - 獲取商店營業時間設定
- PUT /api/admin/settings/store-hours - 更新商店營業時間設定

### 用戶管理
- POST /api/admin/settings/force-logout-users - 強制所有前台用戶登出

### 特殊日期設定
- GET /api/admin/settings/special-dates - 獲取特殊日期列表
- POST /api/admin/settings/special-dates - 新增特殊日期設定
- PUT /api/admin/settings/special-dates/:id - 更新特殊日期設定
- DELETE /api/admin/settings/special-dates/:id - 刪除特殊日期設定

### 音效設定
- GET /api/admin/settings/audio - 獲取音效設定
- PUT /api/admin/settings/audio - 更新音效設定
- GET /api/admin/settings/audio/files - 獲取音效檔案列表
- PUT /api/admin/settings/audio/filename - 更新音效檔案名稱

### 優惠活動管理
- GET /api/admin/settings/promotions - 獲取優惠活動列表
- GET /api/admin/settings/promotions/:id - 獲取特定優惠活動
- POST /api/admin/settings/promotions - 創建新的優惠活動
- PUT /api/admin/settings/promotions/:id - 更新優惠活動
- DELETE /api/admin/settings/promotions/:id - 刪除優惠活動

### 在線用戶統計
- GET /api/admin/online-users/count - 獲取前台在線人數統計
- GET /api/admin/online-users/details - 獲取在線用戶詳細列表

### 商品比價管理
- GET /api/admin/price-comparison/stats - 獲取比價統計資訊
- POST /api/admin/price-comparison/sync-theone - 同步TheOne商品資料
- GET /api/admin/price-comparison/sync-history - 獲取同步歷史記錄
- GET /api/admin/price-comparison/local-products - 獲取本站商品列表
  - 參數：page, pageSize, search, categoryIds
  - 支援商品名稱搜尋和分類篩選
- GET /api/admin/price-comparison/theone-products - 獲取TheOne商品列表
  - 參數：page, pageSize, search, categoryFilter
  - 支援商品名稱搜尋和分類關聯篩選
- GET /api/admin/price-comparison/products - 獲取商品比較數據（原有功能，向後兼容）

### 發票系統
- GET /api/admin/invoices - 獲取發票記錄列表
- GET /api/admin/invoices/current-period - 獲取當前發票週期資訊
- GET /api/admin/invoices/pending-companies - 獲取待開立發票的公司列表
- POST /api/admin/invoices/trigger-weekly - 手動觸發週發票開立
- GET /api/admin/invoices/skipped - 獲取跳過發票記錄列表 (0元訂單)
  - 參數：page, limit, company, startDate, endDate
- POST /api/admin/invoices/manual - 手動開發票
  - 參數：invoiceType（發票類型：2=二聯式，3=三聯式）, buyerName, buyerIdentifier, buyerAddress, buyerTelephoneNumber, buyerEmailAddress, productItems, mainRemark
  - 二聯式發票不需要統一編號，三聯式發票需要公司抬頭和統一編號
  - 二聯式發票稅額為0，三聯式發票計算5%稅額
  - 二聯式發票不顯示客戶資料庫選擇器，適合一般消費者
  - 三聯式發票顯示客戶資料庫選擇器，適合公司客戶
- GET /api/admin/users - 獲取用戶列表
  - 參數：page, limit, search, memberGroup, status, hideInactive, sortField, sortOrder, isCustomerSelector
  - isCustomerSelector=true 時，客戶資料按A-Z排序（admin_name優先，其次full_name）
  - 預設按建立時間降序排列
- GET /api/admin/invoices/lookup-company/:taxId - 統一編號查詢公司抬頭
  - 參數：taxId（8位數統一編號）
  - 回傳：companyName（公司抬頭）, address（公司地址）, status（公司狀態）
  - 使用多個政府開放資料API進行查詢：財政部電子發票API、經濟部商業司API、第三方API
  - 自動驗證統一編號格式，查詢失敗時提供友善錯誤訊息
- 發票API使用 Amego 正式環境
- 公司統編：60775684 (航線國際有限公司)
- API網址：https://invoice-api.amego.tw
- 0元訂單自動跳過發票開立，並記錄到 skipped_invoice_records 表
- **電子郵件寄送功能已修正**：
  - 單筆發票：已正確取得會員電子郵件地址並傳送給光貿API
  - 週發票：已修正從空字串改為取得會員電子郵件地址
  - 新增欄位：BuyerAddress、BuyerTelephoneNumber、MainRemark
  - 測試確認：光貿API可正常寄送發票通知郵件

## WebSocket 事件

### 連接管理
- `connect` - 建立連接（支援訪客模式）
- `disconnect` - 斷開連接
- `user_online` - 用戶上線事件
- `user_offline` - 用戶下線事件
- `user_enter_homepage` - 用戶進入首頁
- `user_leave_homepage` - 用戶離開首頁

### 通知事件
- `new_order` - 新訂單通知
- `product_request_reply_notification` - 產品代尋回覆通知
- `payment_status_updated` - 付款狀態更新通知
- `order_status_updated` - 訂單狀態更新通知
- `announcement_updated` - 公告更新通知
- `user_profile_updated` - 用戶資料更新通知

### 庫存管理
- `reserve_stock` - 預留庫存
- `restore_stock` - 回補庫存

## 檔案上傳
- /uploads/* - 靜態檔案存取（圖片、音效等）

## 優惠活動同步腳本
- `backend/scripts/sync-promotions.js` - 本地與遠端優惠活動同步腳本
  - 功能：將本地優惠活動同步到遠端服務器
  - 用法：`node backend/scripts/sync-promotions.js`
  - 配置：自動獲取管理員token並進行安全同步

## 前端配置管理
### vite.config.js 環境控制
- `useLocalBackend` - 控制是否使用本地後端（預設false）
- `localBackendUrl` - 本地後端地址：http://localhost:8080
- `remoteBackendUrl` - 遠端後端地址：http://************:8080
- 生產環境自動使用相對路徑通過nginx代理

## 注意事項
1. 所有需要認證的API都需要在Header中包含 Authorization: Bearer <token>
2. 管理員API需要管理員權限
3. 檔案上傳使用multipart/form-data格式
4. 所有API回應都是JSON格式
5. 錯誤回應包含error欄位說明錯誤原因
6. WebSocket連接支援 `websocket` 和 `polling` 兩種傳輸方式
7. 前端可通過 `useLocalBackend` 開關控制連接本地或遠端後端
8. 優惠活動可透過同步腳本在本地和遠端之間保持一致

## 其他前台API
- GET /active-promotions - 獲取目前有效的優惠活動

【後台會員管理】
GET /admin/users/:id/orders
- 取得指定會員的購買紀錄、分頁資訊與總消費金額
- 查詢參數：page, limit, status
- 回傳：
  {
    user: { id, username, full_name },
    orders: [...],
    pagination: { currentPage, totalPages, totalItems, pageSize },
    total_spent: number // 該會員所有已完成訂單（完成出貨流程、現場自取、當日配送、取貨未付款）final_amount加總，單位NT$
  }
