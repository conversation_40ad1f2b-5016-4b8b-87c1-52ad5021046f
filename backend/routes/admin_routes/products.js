const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 生成檔案名稱（直接使用SKU，不添加CGP前綴）
function generateFilename(sku) {
  // 如果有SKU，直接返回SKU，否則使用時間戳
  if (sku) {
    return sku;
  } else {
    // 如果沒有SKU，使用時間戳作為檔案名
    const timestamp = Date.now();
    return `product_${timestamp}`;
  }
}

// 此函數接收依賴作為參數
module.exports = ({ productDb, logError, authenticateAdmin }) => {
  const router = express.Router();

  // 設定 multer 儲存配置
  const storage = multer.diskStorage({
    destination: function(req, file, cb) {
      const uploadDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'products');
      // 確保目錄存在
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
      }
      cb(null, uploadDir);
    },
    filename: function(req, file, cb) {
      // 使用SKU作為檔案名稱
      const sku = req.body.sku;
      const ext = path.extname(file.originalname) || '.jpg';
      const filename = generateFilename(sku) + ext;

      // 處理非法字元
      const safeFilename = filename.replace(/[^a-zA-Z0-9.-_]/g, '_');
      cb(null, safeFilename);
    }
  });

  const upload = multer({
    storage: storage,
    limits: { fileSize: 5 * 1024 * 1024 }, // 限制 5MB
    fileFilter: function(req, file, cb) {
      // 僅接受圖片檔案
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('只允許上傳圖片檔案'));
      }
    }
  });

  // 設定用於編輯的上傳中間件
  const editUpload = multer({
    storage: multer.diskStorage({
      destination: function(req, file, cb) {
        const uploadDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'products');
        if (!fs.existsSync(uploadDir)) {
          fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
      },
      filename: function(req, file, cb) {
        // 使用SKU作為檔案名稱
        const sku = req.body.sku || req.query.sku;
        const ext = path.extname(file.originalname) || '.jpg';
        
        // 為了在編輯時避免檔名衝突，我們使用時間戳 + 隨機數
        // 實際的檔名會在處理時根據是否為主圖來重新命名
        const timestamp = Date.now();
        const random = Math.round(Math.random() * 1E6);
        const tempFilename = `temp_${sku}_${timestamp}_${random}${ext}`;

        // 處理非法字元
        const safeFilename = tempFilename.replace(/[^a-zA-Z0-9.-_]/g, '_');
        cb(null, safeFilename);
      }
    }),
    limits: { fileSize: 5 * 1024 * 1024 },
    fileFilter: function(req, file, cb) {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('只允許上傳圖片檔案'));
      }
    }
  });

  // --- Helper functions ---
  async function getAllProductsForCheck() {
    return new Promise((resolve, reject) => {
      productDb.all("SELECT id, category_id FROM product", [], (err, rows) => {
        if (err) {
          console.error("從DB獲取產品進行檢查失敗:", err.message);
          reject(err);
        } else {
          resolve(rows || []);
        }
      });
    });
  }

  // --- Product Routes ---

  // GET /admin/products - 獲取所有產品
  router.get('/', authenticateAdmin, async (req, res) => {
    try {
      const { search, name, sku, categoryId, categoryIds, isActive, page = 1, limit = 10, sortField, sortOrder, no_sales_days } = req.query;
      console.log('🔍 商品API請求參數:', { sortField, sortOrder, page, limit, isActive, no_sales_days });

      // 處理搜尋詞，移除多餘空格，並提取關鍵詞
      function processSearchTerm(term) {
        if (!term) return null;

        // 移除頭尾空格並將多個空格替換為單個空格
        const trimmed = term.trim().replace(/\s+/g, ' ');

        // 如果搜尋詞包含空格，分割為多個關鍵詞
        if (trimmed.includes(' ')) {
          const keywords = trimmed.split(' ').filter(Boolean);
          return {
            original: trimmed,
            keywords: keywords
          };
        }

        return {
          original: trimmed,
          keywords: [trimmed]
        };
      }

      // 處理搜尋詞
      const processedSearch = processSearchTerm(search);
      const processedName = processSearchTerm(name);
      const processedSku = processSearchTerm(sku);

      // 計算分頁參數
      const offset = (page - 1) * limit;
      const parsedLimit = parseInt(limit);

      // --- 動態構建 SQL ---
      // 首先構建計數查詢
      let countSql = `
        SELECT COUNT(DISTINCT p.id) as total
        FROM product p
        LEFT JOIN product_category pc ON p.id = pc.product_id
        LEFT JOIN product_spec ps ON p.id = ps.product_id
      `;

      // 然後構建主查詢
      let sql = `
        WITH RECURSIVE category_path(id, name, parent_id, path) AS (
          SELECT id, name, parent_id, name as path
          FROM category
          WHERE parent_id IS NULL
          UNION ALL
          SELECT c.id, c.name, c.parent_id, cp.path || ' > ' || c.name
          FROM category c
          JOIN category_path cp ON c.parent_id = cp.id
        )
        SELECT
          p.id,
          p.name,
          p.sku,
          p.is_active,
          p.created_at,
          cp.path as category_name,
          pc.category_id,
          pi.image_path as primary_image_path,
          pp.price as retail_price,
          COALESCE(inv.quantity, 0) as stock_quantity,
          p.sort_order,
          COALESCE(sales.total_sales, 0) as sales_volume,
          COALESCE(weekly_sales.weekly_sales, 0) as weekly_sales_volume
        FROM product p
        LEFT JOIN product_category pc ON p.id = pc.product_id
        LEFT JOIN category_path cp ON pc.category_id = cp.id
        LEFT JOIN product_spec ps ON p.id = ps.product_id
        LEFT JOIN (
            SELECT product_id, image_path
            FROM product_image
            WHERE is_primary = 1
        ) AS pi ON p.id = pi.product_id
        LEFT JOIN (
            SELECT product_id, price
            FROM product_price
            WHERE price_type_id = 2
        ) AS pp ON p.id = pp.product_id
        LEFT JOIN (
            SELECT product_id, SUM(quantity) as quantity
            FROM inventory
            GROUP BY product_id
        ) AS inv ON p.id = inv.product_id
        LEFT JOIN (
            SELECT
              oi.product_id,
              SUM(oi.quantity) as total_sales
            FROM order_item oi
            JOIN "order" o ON oi.order_id = o.id
            WHERE o.order_status_id IN (3, 4, 5) AND o.order_status_id NOT IN (6, 7, 8, 9, 10)
            GROUP BY oi.product_id
        ) AS sales ON p.id = sales.product_id
        LEFT JOIN (
            SELECT
              oi.product_id,
              SUM(oi.quantity) as weekly_sales
            FROM order_item oi
            JOIN "order" o ON oi.order_id = o.id
            WHERE o.order_status_id IN (3, 4, 5) AND o.order_status_id NOT IN (6, 7, 8, 9, 10)
              AND datetime(o.order_date) >= datetime('now', '-7 days', '+8 hours')
              AND datetime(o.order_date) <= datetime('now', '+8 hours')
            GROUP BY oi.product_id
        ) AS weekly_sales ON p.id = weekly_sales.product_id
      `;

      const whereClauses = [];
      const params = [];

      // 處理「14 Pro Max 電池」類特殊搜尋詞
      function detectSpecialPhoneTerms(searchTermObj) {
        if (!searchTermObj) return false;

        // 檢查是否有iPhone相關關鍵詞和電池關鍵詞
        const hasIphone = searchTermObj.original.toLowerCase().includes('iphone') ||
                         searchTermObj.original.includes('14') ||
                         searchTermObj.original.includes('13') ||
                         searchTermObj.original.includes('15');

        const hasBattery = searchTermObj.original.includes('電池');

        if (hasIphone && hasBattery) {
          console.log('檢測到特殊手機電池搜尋:', searchTermObj.original);
          return true;
        }

        return false;
      }

      // 使用關鍵詞搜尋的函數
      function buildKeywordSearchClause(field, keywords) {
        let conditions = [];
        let params = [];

        // 優先使用AND邏輯，確保所有關鍵詞都必須匹配
        keywords.forEach(keyword => {
          conditions.push(`${field} LIKE ?`);
          params.push(`%${keyword}%`);
        });

        return {
          sql: `(${conditions.join(' AND ')})`, // 使用AND確保所有關鍵詞同時匹配
          params
        };
      }

      // 簡化搜尋邏輯 - 優先處理綜合搜尋參數
      if (processedSearch) {
        console.log(`添加綜合搜尋條件: "${processedSearch.original}" (同時搜尋名稱和SKU)`);

        // 移除特殊情況處理
        if (processedSearch.keywords.length > 1) {
          // 使用AND邏輯進行多關鍵詞搜尋
          const nameClause = buildKeywordSearchClause('p.name', processedSearch.keywords);

          // 構建規格搜尋條件
          const specClauseConditions = processedSearch.keywords.map(keyword => {
            return `ps.spec_value LIKE ?`;
          });
          const specClauseSql = `(${specClauseConditions.join(' AND ')})`;
          const specParams = processedSearch.keywords.map(keyword => `%${keyword}%`);

          // 組合搜尋條件
          whereClauses.push(`(${nameClause.sql} OR (${specClauseSql}))`);
          params.push(...nameClause.params, ...specParams);

          console.log(`使用關鍵詞搜尋 (${processedSearch.keywords.length} 關鍵詞): 名稱和規格匹配`);
        } else {
          // 單一關鍵詞標準搜尋 - 支援名稱或SKU或規格
          whereClauses.push(`(p.name LIKE ? OR p.sku LIKE ? OR ps.spec_value LIKE ?)`);
          params.push(
            `%${processedSearch.original}%`,
            `%${processedSearch.original}%`,
            `%${processedSearch.original}%`
          );
        }
      }
      // 單獨處理名稱搜尋
      else if (processedName) {
        console.log(`添加名稱搜尋條件: "${processedName.original}"`);

        // 移除特殊情況處理
        if (processedName.keywords.length > 1) {
          // 使用AND邏輯進行多關鍵詞搜尋
          const nameClause = buildKeywordSearchClause('p.name', processedName.keywords);
          whereClauses.push(nameClause.sql);
          params.push(...nameClause.params);
          console.log(`使用關鍵詞搜尋 (${processedName.keywords.length} 關鍵詞): ${nameClause.sql}`);
        } else {
          // 單一關鍵詞標準搜尋
          whereClauses.push(`p.name LIKE ?`);
          params.push(`%${processedName.original}%`);
        }
      }
      // 單獨處理SKU搜尋
      else if (processedSku) {
        console.log(`添加SKU搜尋條件: "${processedSku.original}"`);
        whereClauses.push('p.sku LIKE ?');
        params.push(`%${processedSku.original}%`);
      }

      // 處理分類篩選
      if (categoryIds) {
        // 如果提供了多個分類ID（逗號分隔），則構建 IN 查詢
        const catIds = categoryIds.split(',').map(id => id.trim()).filter(id => id);
        if (catIds.length > 0) {
          whereClauses.push(`pc.category_id IN (${catIds.map(() => '?').join(',')})`);
          params.push(...catIds);
          console.log(`添加分類ID篩選: [${catIds.join(', ')}]`);
        }
      } else if (categoryId) {
        // 保留向後兼容性，支持單一分類ID
        whereClauses.push('pc.category_id = ?');
        params.push(categoryId);
        console.log(`添加單一分類ID篩選: ${categoryId}`);
      }

      if (isActive !== undefined && isActive !== null && isActive !== 'all') {
        // isActive 會是 'true' 或 'false' 字符串，需要轉換
        whereClauses.push('p.is_active = ?');
        params.push(isActive === 'true' ? 1 : 0);
      }

      // 處理無銷售篩選
      if (no_sales_days && !isNaN(parseInt(no_sales_days))) {
        const days = parseInt(no_sales_days);
        console.log(`添加無銷售篩選條件: ${days}天內沒有銷售記錄`);

        // 篩選在指定天數內沒有銷售記錄的商品
        // 使用NOT EXISTS來排除有銷售記錄的商品
        whereClauses.push(`
          NOT EXISTS (
            SELECT 1 FROM order_item oi2
            JOIN "order" o2 ON oi2.order_id = o2.id
            WHERE oi2.product_id = p.id
              AND o2.order_status_id IN (3, 4)
              AND datetime(o2.created_at) >= datetime('now', ? || ' days', '+8 hours')
              AND datetime(o2.created_at) <= datetime('now', '+8 hours')
          )
        `);
        params.push(`-${days}`);
      }

      // 添加 WHERE 子句到 SQL 查詢
      if (whereClauses.length > 0) {
        const whereClause = ' WHERE ' + whereClauses.join(' AND ');
        countSql += whereClause;
        sql += whereClause;
      }

      // 添加 GROUP BY 來確保結果唯一
      sql += ' GROUP BY p.id';

      // 處理排序
      let orderByClause = '';
      if (sortField && sortOrder) {
        // 驗證排序欄位，添加銷量排序支援和廠商進貨優先排序
        const validSortFields = ['sort_order', 'name', 'product_name', 'sku', 'created_at', 'retail_price', 'stock_quantity', 'quantity', 'sales_volume', 'supplier_priority'];
        const validSortOrders = ['asc', 'desc'];

        if (validSortFields.includes(sortField) && validSortOrders.includes(sortOrder.toLowerCase())) {
          if (sortField === 'retail_price') {
            orderByClause = ` ORDER BY pp.price ${sortOrder.toUpperCase()}`;
          } else if (sortField === 'stock_quantity' || sortField === 'quantity') {
            orderByClause = ` ORDER BY inv.quantity ${sortOrder.toUpperCase()}`;
          } else if (sortField === 'sales_volume') {
            orderByClause = ` ORDER BY COALESCE(sales.total_sales, 0) ${sortOrder.toUpperCase()}`;
          } else if (sortField === 'supplier_priority') {
            // 廠商進貨優先排序：七個優先級層次（基於7天週銷量）
            // 第一優先級：0庫存且銷量高的商品 (庫存=0, 7天銷量>5)
            // 第二優先級：1-3庫存且銷量高的商品 (庫存1-3, 7天銷量>5)
            // 第三優先級：0庫存且銷量中等的商品 (庫存=0, 7天銷量1-5)
            // 第四優先級：1-3庫存且銷量中等的商品 (庫存1-3, 7天銷量1-5)
            // 第五優先級：0庫存且銷量低的商品 (庫存=0, 7天銷量<1)
            // 第六優先級：1-3庫存且銷量低的商品 (庫存1-3, 7天銷量<1)
            // 第七優先級：其他不符合上述條件的商品
            // 相同優先級內按商品名稱排序
            console.log('🔄 使用廠商進貨優先級排序（七級分類，基於7天週銷量：高>5，中1-5，低<1）');
            orderByClause = ` ORDER BY
              CASE
                WHEN COALESCE(inv.quantity, 0) = 0 AND COALESCE(weekly_sales.weekly_sales, 0) > 5 THEN 1
                WHEN COALESCE(inv.quantity, 0) BETWEEN 1 AND 3 AND COALESCE(weekly_sales.weekly_sales, 0) > 5 THEN 2
                WHEN COALESCE(inv.quantity, 0) = 0 AND COALESCE(weekly_sales.weekly_sales, 0) BETWEEN 1 AND 5 THEN 3
                WHEN COALESCE(inv.quantity, 0) BETWEEN 1 AND 3 AND COALESCE(weekly_sales.weekly_sales, 0) BETWEEN 1 AND 5 THEN 4
                WHEN COALESCE(inv.quantity, 0) = 0 AND COALESCE(weekly_sales.weekly_sales, 0) < 1 THEN 5
                WHEN COALESCE(inv.quantity, 0) BETWEEN 1 AND 3 AND COALESCE(weekly_sales.weekly_sales, 0) < 1 THEN 6
                ELSE 7
              END ASC,
              p.name ASC`;
          } else if (sortField === 'product_name') {
            orderByClause = ` ORDER BY p.name ${sortOrder.toUpperCase()}`;
          } else {
            orderByClause = ` ORDER BY p.${sortField} ${sortOrder.toUpperCase()}`;
          }
          // 添加次要排序條件
          if (sortField !== 'supplier_priority') {
            orderByClause += ', p.created_at DESC';
          }
          // supplier_priority 的排序條件已經在上面完整定義了，不需要額外的次要條件
        } else {
          // 預設排序：按創建時間降序（最新商品在最上方）
          orderByClause = ' ORDER BY p.created_at DESC';
        }
      } else {
        // 預設排序：按創建時間降序（最新商品在最上方）
        orderByClause = ' ORDER BY p.created_at DESC';
      }

      sql += orderByClause;
      sql += ' LIMIT ? OFFSET ?';

      // 為分頁添加參數
      const queryParams = [...params];
      const countParams = [...params];
      queryParams.push(parsedLimit, offset);

      // 先獲取總記錄數
      productDb.get(countSql, countParams, (countErr, countResult) => {
        if (countErr) {
          logError(countErr, req, res, '獲取產品總數失敗');
          return res.status(500).json({ error: '獲取產品總數失敗' });
        }

        const totalItems = countResult ? countResult.total : 0;
        const totalPages = Math.ceil(totalItems / parsedLimit);
        // 保留吉伊卡特殊處理
        if (totalItems === 0 && (
          (processedSearch?.original && processedSearch.original.includes('吉伊卡')) ||
          (processedName?.original && processedName.original.includes('吉伊卡')))) {
          console.log('無結果但檢測到吉伊卡系列搜尋，使用特殊搜尋...');
          // 直接查詢包含「吉伊卡」的商品
          const directSql = `
            SELECT
              p.id,
              p.name,
              p.sku,
              p.is_active,
              p.created_at,
              null as category_name,
              null as category_id,
              null as primary_image_path,
              null as retail_price,
              0 as stock_quantity,
              0 as sort_order
            FROM product p
            WHERE p.name LIKE '%吉伊卡%'
            ORDER BY p.created_at DESC
            LIMIT ? OFFSET ?
          `;

          productDb.all(directSql, [parsedLimit, offset], (err, specialRows) => {
            if (err) {
              console.error('特殊查詢失敗:', err);
              return productDb.all(sql, queryParams, handleResults);
            }

            console.log(`特殊查詢找到 ${specialRows.length} 筆「吉伊卡」相關商品`);
            if (specialRows.length > 0) {
              specialRows.forEach((product, i) => {
                console.log(`  ${i+1}. ID: ${product.id}, 名稱: ${product.name}, SKU: ${product.sku}`);
              });

              // 格式化結果並返回
              const formattedProducts = specialRows.map(product => ({
                ...product,
                is_active: Boolean(product.is_active),
                retail_price: null,
                primary_image_path: null,
                stock_quantity: 0,
                sort_order: 0,
                // 確保 SKU 沒有前後空白字符
                sku: product.sku ? product.sku.trim() : product.sku
              }));

              return res.json({
                products: formattedProducts,
                pagination: {
                  current_page: parseInt(page),
                  total_pages: Math.ceil(specialRows.length / parsedLimit),
                  total: specialRows.length,
                  page_size: parsedLimit
                }
              });
            } else {
              // 如果特殊查詢也沒結果，回到正常查詢
              return productDb.all(sql, queryParams, handleResults);
            }
          });
          return;
        }

        // 然後獲取當前頁的數據
        productDb.all(sql, queryParams, handleResults);

        function handleResults(err, rows) {
          if (err) {
            logError(err, req, res, '獲取產品列表失敗');
            const errorMessage = err.message;
            return res.status(500).json({ error: errorMessage });
          }

          const products = rows || [];

          // 將 is_active 轉換為布林值並格式化價格
          const formattedProducts = products.map(product => ({
            ...product,
            is_active: Boolean(product.is_active),
            retail_price: product.retail_price === null || product.retail_price === undefined ? null : Number(product.retail_price),
            // 確保 image_path 是 null 或有效字串
            primary_image_path: product.primary_image_path || null,
            // 確保 SKU 沒有前後空白字符
            sku: product.sku ? product.sku.trim() : product.sku
          }));

          // 輸出前10個結果的名稱和SKU，方便調試

          if (formattedProducts.length > 0) {
            formattedProducts.slice(0, 10).forEach((product, index) => {
            });
          } else {
            console.log(`  沒有找到匹配的商品`);

            // 如果有搜尋條件但沒找到結果，嘗試進行額外調試
            if (processedSearch || processedName || processedSku) {
              console.log(`  調試信息: 嘗試直接從數據庫查詢匹配商品...`);

              // 執行一個簡單的查詢以驗證數據庫中是否有相似商品
              const debugSql = `
                SELECT id, name, sku FROM product
                WHERE name LIKE ? OR sku LIKE ?
                LIMIT 10
              `;
              const searchTerm = processedSearch?.original || processedName?.original || processedSku?.original || '';
              const debugParams = [`%${searchTerm}%`, `%${searchTerm}%`];

              productDb.all(debugSql, debugParams, (debugErr, debugRows) => {
                if (debugErr) {
                  console.log(`  調試查詢失敗:`, debugErr.message);
                } else {
                  console.log(`  寬鬆搜尋結果 (${debugRows.length} 筆):`);
                  if (debugRows.length > 0) {
                    debugRows.forEach((p, i) => {
                      console.log(`    ${i + 1}. ID: ${p.id}, 名稱: ${p.name}, SKU: ${p.sku}`);
                    });
                  } else {
                    console.log(`    數據庫中沒有任何接近的匹配項`);
                  }
                }
              });
            }
          }

          res.json({
              products: formattedProducts,
              pagination: {
                current_page: parseInt(page),
                total_pages: totalPages,
                total: totalItems,
                page_size: parsedLimit
              }
            });
        }
      });
    } catch (err) {
      logError(err, req, res, '處理產品列表請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：獲取商品統計數據 - 必須放在動態路由之前
  router.get('/stats', authenticateAdmin, (req, res) => {
    try {
      const sql = `
        SELECT
          COUNT(*) as total_products,
          SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_products,
          SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive_products
        FROM product
      `;

      productDb.get(sql, [], (err, result) => {
        if (err) {
          logError(err, req, res, '獲取商品統計數據失敗');
          return res.status(500).json({ error: '獲取商品統計數據失敗' });
        }

        res.json({
          success: true,
          data: {
            total_products: result.total_products || 0,
            active_products: result.active_products || 0,
            inactive_products: result.inactive_products || 0
          }
        });
      });
    } catch (err) {
      logError(err, req, res, '處理商品統計請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 獲取商品總數 - 用於盤點管理 - 必須放在動態路由之前
  router.get('/count', authenticateAdmin, (req, res) => {
    const sql = `SELECT COUNT(*) as total FROM product WHERE is_active = 1`;

    productDb.get(sql, [], (err, result) => {
      if (err) {
        console.error('獲取商品總數失敗:', err);
        logError(err, req, res, '獲取商品總數失敗');
        return res.status(500).json({
          success: false,
          message: '獲取商品總數失敗'
        });
      }

      res.json({
        success: true,
        data: {
          total: result.total
        }
      });
    });
  });

  // GET /admin/products/:id - 獲取單個產品
  router.get('/:id', authenticateAdmin, (req, res) => {
    try {
      const productId = req.params.id;
      // 修改 SQL 查詢，包含圖片和價格信息，類似於列表頁的查詢
      const sql = `
        WITH RECURSIVE category_path(id, name, parent_id, path) AS (
          SELECT id, name, parent_id, name as path
          FROM category
          WHERE parent_id IS NULL
          UNION ALL
          SELECT c.id, c.name, c.parent_id, cp.path || ' > ' || c.name
          FROM category c
          JOIN category_path cp ON c.parent_id = cp.id
        )
        SELECT
          p.*,
          cp.path as category_name,
          cp.id as category_id,
          pi.image_path as primary_image_path,
          pp.price as retail_price
        FROM product p
        LEFT JOIN product_category pc ON p.id = pc.product_id
        LEFT JOIN category_path cp ON pc.category_id = cp.id
        LEFT JOIN (
            SELECT product_id, image_path
            FROM product_image
            WHERE is_primary = 1
        ) AS pi ON p.id = pi.product_id
        LEFT JOIN (
            SELECT product_id, price
            FROM product_price
            WHERE price_type_id = 2
        ) AS pp ON p.id = pp.product_id
        WHERE p.id = ?
      `;

      productDb.get(sql, [productId], (err, product) => {
        if (err) {
          logError(err, req, res, `獲取產品ID ${productId} 失敗`);
          return res.status(500).json({ error: '獲取產品失敗' });
        }
        if (!product) {
          return res.status(404).json({ error: '找不到該產品' });
        }

        // 處理欄位轉換
        if (product) {
          product.is_active = Boolean(product.is_active);
          // 確保零售價格正確轉換為數字類型
          if (product.retail_price !== null && product.retail_price !== undefined) {
            product.retail_price = Number(product.retail_price);
          } else {
            product.retail_price = null; // 明確設置為 null，避免未定義
          }
        }

        res.json(product);
      });
    } catch (err) {
      logError(err, req, res, '處理單個產品請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // POST /admin/products - 創建新產品
  router.post('/', authenticateAdmin, (req, res) => {
    // 用於追蹤非主圖的序號
    let imageCounter = 0;

    // 使用自定義處理代替 multer 中間件，支持裁切後的圖片
    const upload = multer({
      storage: multer.diskStorage({
        destination: function(req, file, cb) {
          const uploadDir = path.join(__dirname, '..', '..', 'public', 'uploads', 'products');
          if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
          }
          cb(null, uploadDir);
        },
        filename: function(req, file, cb) {
          // 使用SKU作為檔案名稱
          const sku = req.body.sku;
          const ext = path.extname(file.originalname) || '.jpg';

          // 檢查是否為主圖
          const isPrimary = file.fieldname === 'primary_image';

          let filename;
          if (isPrimary) {
            // 主圖直接使用 SKU 名稱
            filename = generateFilename(sku) + ext;
          } else {
            // 非主圖加上序號後綴
            imageCounter++;
            filename = generateFilename(sku) + '_' + imageCounter + ext;
          }

          // 處理非法字元
          const safeFilename = filename.replace(/[^a-zA-Z0-9.-_]/g, '_');

          cb(null, safeFilename);
        }
      }),
      limits: { fileSize: 5 * 1024 * 1024 },
      fileFilter: function(req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
          cb(null, true);
        } else {
          cb(new Error('只允許上傳圖片檔案'));
        }
      }
    }).fields([
      { name: 'images', maxCount: 9 },
      { name: 'primary_image', maxCount: 1 }
    ]);

    upload(req, res, async (err) => {
      if (err) {
        if (err instanceof multer.MulterError) {
          // Multer 錯誤
          return res.status(400).json({ error: `上傳圖片錯誤: ${err.message}` });
        } else {
          // 其他錯誤
          return res.status(500).json({ error: `上傳圖片時發生錯誤: ${err.message}` });
        }
      }

    try {
      // 獲取請求中的數據
      const { name, description, category_id, retail_price, cost_price, sku, spec, color, is_active } = req.body;

      // 驗證必要欄位
      if (!name) {
        return res.status(400).json({ error: '產品名稱不能為空' });
      }

      // 檢查所有必需值
      const missingFields = [];
      if (!name) missingFields.push('產品名稱');
      if (!spec) missingFields.push('規格');
      if (!sku) missingFields.push('SKU');
      if (!category_id) missingFields.push('分類');

      if (missingFields.length > 0) {
        return res.status(400).json({
          error: `缺少必要欄位: ${missingFields.join(', ')}`
        });
      }

      // 驗證SKU是否已存在
      const existingSku = await new Promise((resolve, reject) => {
        productDb.get('SELECT id FROM product WHERE sku = ?', [sku], (err, row) => {
          if (err) {
            console.error("檢查SKU存在性時出錯:", err);
            reject(err);
          } else {
            resolve(row);
          }
        });
      });

      if (existingSku) {
        return res.status(409).json({ error: `SKU "${sku}" 已存在，請使用不同的SKU` });
      }

      // 獲取資料表結構
      const columnNames = await new Promise((resolve, reject) => {
        productDb.all("PRAGMA table_info(product)", (err, columns) => {
          if (err) {
            console.error("獲取產品表列名時出錯:", err);
            reject(err);
          } else {
            resolve(columns.map(col => col.name));
          }
        });
      });

      // 準備插入資料
      const now = new Date().toISOString();
      const hasPrice = columnNames.includes('price');
      const hasBrandId = columnNames.includes('brand_id');

      // 獲取下一個排序值
      const nextSortOrder = await getNextSortOrder();

      // 構建SQL語句
      let fields = ['name', 'description', 'sku', 'sort_order'];
      let values = ['?', '?', '?', '?'];
      let sqlParams = [name, description || '', sku, nextSortOrder];

        // 加入商品顏色
        if (color) {
          fields.push('color');
          values.push('?');
          sqlParams.push(color);
        }

      if (hasPrice) {
        fields.push('price');
        values.push('?');
        sqlParams.push(retail_price !== undefined && retail_price !== null ? retail_price : 0);
      }

      if (hasBrandId) {
        fields.push('brand_id');
        values.push('?');
        sqlParams.push(req.body.brand_id || null);
      }

      if (columnNames.includes('is_active')) {
        fields.push('is_active');
        values.push('?');
        sqlParams.push(is_active === undefined ? 1 : (is_active ? 1 : 0));
      }

      if (columnNames.includes('created_at')) {
        fields.push('created_at');
        values.push('?');
        sqlParams.push(now);
      }

      if (columnNames.includes('updated_at')) {
        fields.push('updated_at');
        values.push('?');
        sqlParams.push(now);
      }

      if (columnNames.includes('spec_name')) {
        fields.push('spec_name');
        values.push('?');
        sqlParams.push(null);
      }

      if (columnNames.includes('spec_color')) {
        fields.push('spec_color');
        values.push('?');
        sqlParams.push(null);
      }

      if (columnNames.includes('category_id')) {
        fields.push('category_id');
        values.push('?');
        sqlParams.push(null);
      }

      const sql = `INSERT INTO product (${fields.join(', ')}) VALUES (${values.join(', ')})`;

      // 執行主產品插入
      const productId = await new Promise((resolve, reject) => {
        productDb.run(sql, sqlParams, function(err) {
          if (err) {
            console.error("SQL 執行錯誤:", err.message);
            reject(err);
          } else {
            resolve(this.lastID);
          }
        });
      });

      console.log("主產品記錄創建成功，ID:", productId);

      // 處理關聯數據
      const promises = [];
      const currentDate = new Date().toISOString().split('T')[0];

      // 1. 插入商品分類關聯
      if (category_id) {
        promises.push(new Promise((resolve, reject) => {
          productDb.get('SELECT id FROM category WHERE id = ?', [category_id], (catErr, catRow) => {
            if (catErr) return reject(new Error(`檢查分類ID ${category_id} 時出錯: ${catErr.message}`));
            if (!catRow) return reject(new Error(`分類ID ${category_id} 不存在`));

            productDb.run(
              `INSERT INTO product_category (product_id, category_id) VALUES (?, ?)`,
              [productId, category_id],
              (linkErr) => {
                if (linkErr) reject(new Error(`插入商品分類關聯失敗: ${linkErr.message}`));
                else resolve();
              }
            );
          });
        }));
      }

        // 2. 處理商品圖片上傳 - 支持裁切後的圖片和主圖標記
        const uploadedFiles = [];

        // 處理主圖
        if (req.files && req.files.primary_image && req.files.primary_image.length > 0) {
          uploadedFiles.push({
            ...req.files.primary_image[0],
            isPrimary: true
          });
        }

        // 處理其他圖片
        if (req.files && req.files.images && req.files.images.length > 0) {
          req.files.images.forEach(file => {
            uploadedFiles.push({
              ...file,
              isPrimary: false
            });
          });
        }

        if (uploadedFiles.length > 0) {
        promises.push(new Promise(async (resolve, reject) => {
          try {
            const imagePromises = uploadedFiles.map((file, index) => {
              return new Promise((res, rej) => {
                const imagePath = `/uploads/products/${file.filename}`;
                  const isPrimary = file.isPrimary ? 1 : 0;
                  const displayOrder = file.isPrimary ? 0 : index + 1;

                productDb.run(
                  `INSERT INTO product_image (product_id, image_path, is_primary, display_order) VALUES (?, ?, ?, ?)`,
                    [productId, imagePath, isPrimary, displayOrder],
                  (err) => {
                    if (err) {
                        console.error(`插入圖片記錄失敗: ${err.message}`);
                        rej(err);
                      } else {
                    res();
                      }
                  }
                );
              });
            });

            await Promise.all(imagePromises);
            resolve();
            } catch (error) {
              reject(error);
          }
        }));
      }

      // 3. 插入零售價
      if (retail_price !== undefined && retail_price !== null && retail_price !== '') {
        promises.push(new Promise((resolve, reject) => {
          productDb.run(
            `INSERT INTO product_price (product_id, price_type_id, price, effective_date) VALUES (?, 2, ?, ?)`,
            [productId, retail_price, currentDate],
            (err) => {
              if (err) reject(new Error(`插入零售價失敗: ${err.message}`));
              else resolve();
            }
          );
        }));
      }

      // 4. 插入成本價
      if (cost_price !== undefined && cost_price !== null && cost_price !== '') {
        promises.push(new Promise((resolve, reject) => {
          productDb.run(
            `INSERT INTO product_price (product_id, price_type_id, price, effective_date) VALUES (?, 1, ?, ?)`,
            [productId, cost_price, currentDate],
            (err) => {
              if (err) reject(new Error(`插入成本價失敗: ${err.message}`));
              else resolve();
            }
          );
        }));
      }

      // 5. 插入規格
      if (spec && spec.trim() !== '') {
        promises.push(new Promise((resolve, reject) => {
          const specItems = spec.split(',').map(item => item.trim()).filter(item => item);

          if (specItems.length === 0) {
            return resolve();
          }

          const insertPromises = specItems.map((specValue, index) => {
            return new Promise((resolveInsert, rejectInsert) => {
              productDb.run(
                'INSERT INTO product_spec (product_id, spec_name, spec_value, display_order) VALUES (?, ?, ?, ?)',
                [productId, '規格', specValue, index],
                (insertErr) => {
                  if (insertErr) rejectInsert(insertErr);
                  else resolveInsert();
                }
              );
            });
          });

          Promise.all(insertPromises)
            .then(() => resolve())
            .catch(err => reject(err));
        }));
      }

      // 6. 插入顏色
      if (color && color.trim() !== '') {
        promises.push(new Promise((resolve, reject) => {
          productDb.run(
            `INSERT INTO product_spec (product_id, spec_name, spec_value) VALUES (?, ?, ?)`,
            [productId, '顏色', color],
            (err) => {
              if (err) {
                console.error('插入顏色失敗:', err);
                reject(new Error(`插入顏色失敗: ${err.message}`));
              } else {
                resolve();
              }
            }
          );
        }));
      }

      // 7. 創建預設庫存記錄（新增商品時）
      promises.push(new Promise((resolve, reject) => {
        // 檢查是否有預設倉庫（ID為1的台北倉）
        productDb.get('SELECT id FROM warehouse WHERE id = 1', [], (err, warehouse) => {
          if (err) {
            console.error('檢查預設倉庫失敗:', err);
            return reject(err);
          }

          const warehouseId = warehouse ? 1 : null;
          if (warehouseId) {
            // 創建預設庫存記錄（數量為0）
            productDb.run(
              'INSERT INTO inventory (product_id, warehouse_id, quantity, safety_stock, last_updated) VALUES (?, ?, 0, 0, CURRENT_TIMESTAMP)',
              [productId, warehouseId],
              (invErr) => {
                if (invErr) {
                  console.error('創建預設庫存記錄失敗:', invErr);
                  return reject(invErr);
                }
                resolve();
              }
            );
          } else {
            resolve(); // 如果沒有倉庫，跳過庫存設置
          }
        });
      }));

      // 8. 處理圖片更新
      if (req.files && req.files.length > 0) {
        const uploadedFiles = req.files;
        promises.push(new Promise(async (resolve, reject) => {
          try {
            // 獲取現有圖片數量
            const currentImagesResult = await new Promise((res, rej) => {
              productDb.all('SELECT id, is_primary FROM product_image WHERE product_id = ? ORDER BY display_order ASC',
                [productId],
                (err, rows) => {
                  if (err) return rej(err);
                  res(rows || []);
                });
            });

            // 獲取當前商品的SKU，用於更新圖片文件名
            if (!req.body.sku && !req.query.sku) {
              const productResult = await new Promise((res, rej) => {
                productDb.get('SELECT sku FROM product WHERE id = ?', [productId], (err, row) => {
                  if (err) return rej(err);
                  res(row || {});
                });
              });

              // 如果有SKU，設置到請求中，供filename函數使用
              if (productResult && productResult.sku) {
                req.body.sku = productResult.sku;
              }
            }

            const hasPrimaryImage = currentImagesResult.some(img => img.is_primary === 1);
            const startOrder = currentImagesResult.length;

            // 處理每個上傳的圖片
            const imagePromises = uploadedFiles.map((file, index) => {
              return new Promise((res, rej) => {
                const imagePath = `/uploads/products/${file.filename}`;
                const isPrimary = !hasPrimaryImage && index === 0 ? 1 : 0; // 如果沒有主圖，第一張為主圖
                const displayOrder = startOrder + index;

                productDb.run(
                  'INSERT INTO product_image (product_id, image_path, is_primary, display_order) VALUES (?, ?, ?, ?)',
                  [productId, imagePath, isPrimary, displayOrder],
                  function(err) {
                    if (err) return rej(err);
                    res();
                  }
                );
              });
            });

            await Promise.all(imagePromises);
            resolve();
          } catch (err) {
            reject(err);
          }
        }));
      }

      // 執行所有關聯更新
      try {
        await Promise.all(promises);

        // 獲取更新後的產品數據
        const updatedProduct = await new Promise((resolve, reject) => {
          productDb.get(
            `SELECT
              p.*,
              (SELECT image_path FROM product_image WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image_path
             FROM product p WHERE p.id = ?`,
            [productId],
            (err, product) => {
              if (err) reject(err);
              else resolve(product || null);
            }
          );
        });

        if (!updatedProduct) {
          return res.status(404).json({ error: '更新後無法找到該產品' });
        }

        // 將 is_active 轉換為布林值
        if (updatedProduct) {
          updatedProduct.is_active = Boolean(updatedProduct.is_active);
        }

        res.status(200).json({
          message: `產品 ID ${productId} 更新成功`,
          product: updatedProduct
        });
      } catch (promiseErr) {
        logError(promiseErr, req, res, `更新產品 ${productId} 關聯數據時出錯`);
        return res.status(500).json({ error: `更新產品關聯數據時出錯: ${promiseErr.message}` });
      }
    } catch (err) {
      logError(err, req, res, '處理創建產品請求時出錯');
      res.status(500).json({ error: `伺服器內部錯誤: ${err.message}` });
    }
    });
  });

  // PUT /admin/products/:id - 更新產品
  router.put('/:id', authenticateAdmin, editUpload.array('images', 10), async (req, res) => {
    try {
      const productId = req.params.id;
      const updateData = req.body; // 獲取請求體中的所有數據

      // 檢查產品是否存在
      const productExists = await new Promise((resolve, reject) => {
        productDb.get('SELECT id, sku FROM product WHERE id = ?', [productId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (!productExists) {
        return res.status(404).json({ error: '找不到要更新的產品' });
      }

      // 儲存原始SKU，用於後續比較
      const originalSku = productExists.sku;
      const newSku = updateData.sku;

      // --- 檢查要更新的數據 ---
      // 允許更新的 product 表直屬欄位 (根據實際情況調整)
      const allowedDirectUpdates = ['name', 'description', 'sku', 'price', 'brand_id', 'is_active', 'sort_order'];
      const setClauses = [];
      const params = [];
      let hasValidUpdate = false;

      // 遍歷請求體中的鍵
      for (const key in updateData) {
        // 只處理定義在 allowedDirectUpdates 中的欄位
        if (allowedDirectUpdates.includes(key)) {
          hasValidUpdate = true;
          if (key === 'is_active') {
            setClauses.push('is_active = ?');
            // 確保轉換為 1 或 0
            params.push(updateData[key] ? 1 : 0);
          } else {
            // 其他欄位直接加入 SET 子句
            setClauses.push(`${key} = ?`);
            params.push(updateData[key] === undefined ? null : updateData[key]);
          }
        }
      }

      // 添加 updated_at 時間戳
      setClauses.push('updated_at = datetime(\'now\')');

      // 執行主產品表更新
      if (setClauses.length > 0) {
        const sql = `UPDATE product SET ${setClauses.join(', ')} WHERE id = ?`;
        params.push(productId); // 將 productId 加到參數列表末尾 for WHERE 子句

        await new Promise((resolve, reject) => {
      productDb.run(sql, params, function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          });
        });
      }

      // 處理各種關聯數據更新
      const promises = [];
      const currentDate = new Date().toISOString().split('T')[0];

      // 當SKU改變時，更新所有相關圖片的文件名
      if (newSku && originalSku !== newSku) {
        promises.push(new Promise(async (resolve, reject) => {
          try {
            // 1. 獲取該產品的所有圖片記錄
            const images = await new Promise((res, rej) => {
              productDb.all('SELECT id, image_path FROM product_image WHERE product_id = ?',
                [productId], (err, rows) => {
                  if (err) rej(err);
                  res(rows || []);
                });
            });

            console.log(`為產品 ${productId} 找到 ${images.length} 張圖片需要更新 SKU: ${originalSku} -> ${newSku}`);

            // 2. 對每張圖片進行處理
            for (const image of images) {
              try {
                // 解析原始文件路徑
                const originalPath = image.image_path;
                const publicPath = path.join(__dirname, '..', '..', 'public', originalPath);

                console.log(`處理圖片 ID ${image.id}, 路徑: ${originalPath}`);

                // 檢查文件是否存在
                if (fs.existsSync(publicPath)) {
                  // 解析文件名和擴展名
                  const dirName = path.dirname(originalPath);
                  const fileName = path.basename(originalPath);
                  const extName = path.extname(fileName);

                  // 生成新的檔案名稱（直接使用SKU）
                  let newFileName;
                  if (fileName.includes('CGP')) {
                    // 如果原檔名是CGP格式，則移除CGP前綴，只保留SKU部分
                    const cgpMatch = fileName.match(/CGP\d{4}_(.+)/);
                    if (cgpMatch && cgpMatch[1]) {
                      // 提取CGP前綴後的部分作為新檔名
                      newFileName = cgpMatch[1];
                    } else {
                      newFileName = generateFilename(newSku) + extName;
                    }
                  } else {
                    // 如果不是CGP格式，則直接使用SKU
                    newFileName = generateFilename(newSku) + extName;
                  }
                  const newRelativePath = `${dirName}/${newFileName}`;
                  const newPublicPath = path.join(__dirname, '..', '..', 'public', newRelativePath);

                  // 檢查目標文件是否已存在，如果存在則先刪除
                  if (fs.existsSync(newPublicPath) && publicPath !== newPublicPath) {
                    console.log(`目標文件已存在，刪除: ${newPublicPath}`);
                    fs.unlinkSync(newPublicPath);
                  }

                  // 重命名文件
                  fs.renameSync(publicPath, newPublicPath);

                  // 更新數據庫中的路徑
                  await new Promise((res, rej) => {
                    productDb.run('UPDATE product_image SET image_path = ? WHERE id = ?',
                      [newRelativePath, image.id], (err) => {
                        if (err) rej(err);
                        else res();
                      });
                  });

                  console.log(`已更新圖片: ${originalPath} -> ${newRelativePath}`);
                } else {
                  console.log(`圖片文件不存在: ${publicPath}`);
                  // 即使文件不存在，仍然更新數據庫中的路徑名稱以保持一致性
                  const dirName = path.dirname(originalPath);
                  const fileName = path.basename(originalPath);
                  const extName = path.extname(fileName);

                  let newFileName;
                  if (fileName.includes('CGP')) {
                    // 如果原檔名是CGP格式，則移除CGP前綴，只保留SKU部分
                    const cgpMatch = fileName.match(/CGP\d{4}_(.+)/);
                    if (cgpMatch && cgpMatch[1]) {
                      // 提取CGP前綴後的部分作為新檔名
                      newFileName = cgpMatch[1];
                    } else {
                      newFileName = generateFilename(newSku) + extName;
                    }
                  } else {
                    newFileName = generateFilename(newSku) + extName;
                  }
                  const newRelativePath = `${dirName}/${newFileName}`;

                  // 更新數據庫中的路徑
                  await new Promise((res, rej) => {
                    productDb.run('UPDATE product_image SET image_path = ? WHERE id = ?',
                      [newRelativePath, image.id], (err) => {
                        if (err) rej(err);
                        else res();
                      });
                  });

                  console.log(`已更新圖片路徑（僅數據庫）: ${originalPath} -> ${newRelativePath}`);
                }
              } catch (imageError) {
                console.error(`處理圖片 ${image.id} 時出錯:`, imageError);
                // 繼續處理其他圖片
              }
            }

            resolve();
          } catch (error) {
            console.error('更新圖片文件名失敗:', error);
            reject(error);
          }
        }));
      }

      // 1. 處理規格更新
      if (updateData.spec) {
        promises.push(new Promise((resolve, reject) => {
          // 先刪除該產品的所有規格
          productDb.run('DELETE FROM product_spec WHERE product_id = ? AND spec_name = ?',
            [productId, '規格'], (deleteErr) => {
              if (deleteErr) return reject(deleteErr);

              // 分割規格字串，以逗號為分隔符
              const specItems = updateData.spec.split(',').map(item => item.trim()).filter(item => item);

              if (specItems.length === 0) {
                return resolve(); // 如果沒有規格項，直接結束
              }

              // 建立批量插入的SQL語句和參數
              const insertPromises = specItems.map((specValue, index) => {
                return new Promise((resolveInsert, rejectInsert) => {
                  productDb.run(
                    'INSERT INTO product_spec (product_id, spec_name, spec_value, display_order) VALUES (?, ?, ?, ?)',
                    [productId, '規格', specValue, index],
                    (insertErr) => {
                      if (insertErr) rejectInsert(insertErr);
                      else resolveInsert();
                    }
                  );
                });
              });

              // 執行所有插入操作
              Promise.all(insertPromises)
                .then(() => resolve())
                .catch(err => reject(err));
            });
        }));
      }

      // 2. 處理顏色更新
      if (updateData.color) {
        promises.push(new Promise((resolve, reject) => {
          // 檢查是否存在顏色記錄
          productDb.get('SELECT id FROM product_spec WHERE product_id = ? AND spec_name = ?',
            [productId, '顏色'],
            (err, row) => {
              if (err) return reject(err);

              if (row) {
                // 更新現有顏色
                productDb.run('UPDATE product_spec SET spec_value = ? WHERE id = ?',
                  [updateData.color, row.id],
                  (updateErr) => {
                    if (updateErr) reject(updateErr);
                    else resolve();
                  });
              } else {
                // 新增顏色
                productDb.run('INSERT INTO product_spec (product_id, spec_name, spec_value) VALUES (?, ?, ?)',
                  [productId, '顏色', updateData.color],
                  (insertErr) => {
                    if (insertErr) reject(insertErr);
                    else resolve();
                  });
        }
            });
        }));
      }

      // 3. 處理分類更新
      if (updateData.category_id) {
        promises.push(new Promise((resolve, reject) => {
          // 先檢查分類是否存在
          productDb.get('SELECT id FROM category WHERE id = ?', [updateData.category_id],
            (catErr, catRow) => {
              if (catErr) return reject(catErr);
              if (!catRow) return reject(new Error(`分類 ID ${updateData.category_id} 不存在`));

              // 檢查產品-分類關聯是否存在
              productDb.get('SELECT category_id FROM product_category WHERE product_id = ?',
                [productId],
                (linkErr, linkRow) => {
                  if (linkErr) return reject(linkErr);

                  if (linkRow) {
                    // 更新現有關聯
                    productDb.run('UPDATE product_category SET category_id = ? WHERE product_id = ?',
                      [updateData.category_id, productId],
                      (updateErr) => {
                        if (updateErr) reject(updateErr);
                        else resolve();
                      });
          } else {
                    // 新增關聯
                    productDb.run('INSERT INTO product_category (product_id, category_id) VALUES (?, ?)',
                      [productId, updateData.category_id],
                      (insertErr) => {
                        if (insertErr) reject(insertErr);
                        else resolve();
                      });
          }
        });
      });
        }));
      }

      // 4. 處理零售價更新
      if (updateData.retail_price !== undefined && updateData.retail_price !== null) {
        promises.push(new Promise((resolve, reject) => {
          // 檢查是否存在零售價
          productDb.get('SELECT id FROM product_price WHERE product_id = ? AND price_type_id = 2',
            [productId],
            (err, row) => {
              if (err) return reject(err);

              if (row) {
                // 更新現有零售價
                productDb.run('UPDATE product_price SET price = ?, effective_date = ? WHERE id = ?',
                  [updateData.retail_price, currentDate, row.id],
                  (updateErr) => {
                    if (updateErr) reject(updateErr);
                    else resolve();
                  });
              } else {
                // 新增零售價
                productDb.run('INSERT INTO product_price (product_id, price_type_id, price, effective_date) VALUES (?, 2, ?, ?)',
                  [productId, updateData.retail_price, currentDate],
                  (insertErr) => {
                    if (insertErr) reject(insertErr);
                    else resolve();
                  });
              }
            });
        }));
      }

      // 5. 處理成本價更新
      if (updateData.cost_price !== undefined && updateData.cost_price !== null) {
        promises.push(new Promise((resolve, reject) => {
          // 檢查是否存在成本價
          productDb.get('SELECT id FROM product_price WHERE product_id = ? AND price_type_id = 1',
            [productId],
            (err, row) => {
              if (err) return reject(err);

              if (row) {
                // 更新現有成本價
                productDb.run('UPDATE product_price SET price = ?, effective_date = ? WHERE id = ?',
                  [updateData.cost_price, currentDate, row.id],
                  (updateErr) => {
                    if (updateErr) reject(updateErr);
                    else resolve();
                  });
              } else {
                // 新增成本價
                productDb.run('INSERT INTO product_price (product_id, price_type_id, price, effective_date) VALUES (?, 1, ?, ?)',
                  [productId, updateData.cost_price, currentDate],
                  (insertErr) => {
                    if (insertErr) reject(insertErr);
                    else resolve();
                  });
              }
            });
        }));
      }

      // 6. 處理庫存更新
      if (updateData.stock_quantity !== undefined && updateData.stock_quantity !== null) {
        promises.push(new Promise(async (resolve, reject) => {
          try {
            // 首先檢查是否存在庫存記錄
            const inventoryRow = await new Promise((res, rej) => {
              productDb.get('SELECT id, quantity, warehouse_id FROM inventory WHERE product_id = ? LIMIT 1',
                [productId], (err, row) => {
                  if (err) rej(err);
                  else res(row);
                });
            });

            const warehouseId = inventoryRow ? inventoryRow.warehouse_id : 1; // 默認倉庫ID為1
            const previousQuantity = inventoryRow ? inventoryRow.quantity : 0;
            const newQuantity = parseInt(updateData.stock_quantity);
            const quantityChange = newQuantity - previousQuantity;

            if (inventoryRow) {
              // 更新現有庫存
              await new Promise((res, rej) => {
                productDb.run('UPDATE inventory SET quantity = ?, last_updated = CURRENT_TIMESTAMP WHERE id = ?',
                  [newQuantity, inventoryRow.id], (err) => {
                    if (err) rej(err);
                    else res();
                  });
              });
            } else {
              // 創建新的庫存記錄
              await new Promise((res, rej) => {
                productDb.run('INSERT INTO inventory (product_id, warehouse_id, quantity, safety_stock, last_updated) VALUES (?, ?, ?, 0, CURRENT_TIMESTAMP)',
                  [productId, warehouseId, newQuantity], (err) => {
                    if (err) rej(err);
                    else res();
                  });
              });
            }

            // 如果數量有變化，記錄庫存異動歷史
            if (quantityChange !== 0) {
              // 確保手動調整的異動類型存在
              await new Promise((res, rej) => {
                productDb.run(`
                  INSERT OR IGNORE INTO movement_type (name, description, affect_quantity, is_active)
                  VALUES ('手動調整', '後台管理員手動調整庫存', 0, 1)
                `, (err) => {
                  if (err) rej(err);
                  else res();
                });
              });

              // 獲取手動調整的異動類型ID
              const movementType = await new Promise((res, rej) => {
                productDb.get('SELECT id FROM movement_type WHERE name = ?', ['手動調整'], (err, row) => {
                  if (err) rej(err);
                  else res(row);
                });
              });

              if (movementType) {
                // 記錄庫存異動歷史
                await new Promise((res, rej) => {
                  productDb.run(`
                    INSERT INTO stock_movement (
                      product_id, warehouse_id, movement_type_id, quantity,
                      previous_quantity, new_quantity, reference_type,
                      notes, created_by, created_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now', '+8 hours'))
                  `, [
                    productId,
                    warehouseId,
                    movementType.id,
                    quantityChange, // 這裡改為直接寫入正負數
                    previousQuantity,
                    newQuantity,
                    'manual_adjustment',
                    '後台商品管理頁面手動調整庫存',
                    req.admin ? req.admin.id : null
                  ], (err) => {
                    if (err) rej(err);
                    else res();
                  });
                });
              }
            }

            resolve();
          } catch (error) {
            console.error('處理庫存更新失敗:', error);
            reject(error);
          }
        }));
      }

      // 7. 處理圖片更新
      if (req.files && req.files.length > 0) {
        const uploadedFiles = req.files;
        promises.push(new Promise(async (resolve, reject) => {
          try {
            // 獲取現有圖片數量
            const currentImagesResult = await new Promise((res, rej) => {
              productDb.all('SELECT id, is_primary FROM product_image WHERE product_id = ? ORDER BY display_order ASC',
                [productId],
                (err, rows) => {
                  if (err) return rej(err);
                  res(rows || []);
                });
            });

            // 獲取當前商品的SKU，用於更新圖片文件名
            if (!req.body.sku && !req.query.sku) {
              const productResult = await new Promise((res, rej) => {
                productDb.get('SELECT sku FROM product WHERE id = ?', [productId], (err, row) => {
                  if (err) return rej(err);
                  res(row || {});
                });
              });

              // 如果有SKU，設置到請求中，供filename函數使用
              if (productResult && productResult.sku) {
                req.body.sku = productResult.sku;
              }
            }

            const hasPrimaryImage = currentImagesResult.some(img => img.is_primary === 1);
            const startOrder = currentImagesResult.length;

            // 計算已存在的非主圖數量，用於生成新圖片的序號
            const existingNonPrimaryCount = currentImagesResult.filter(img => img.is_primary === 0).length;

            // 處理每個上傳的圖片
            const imagePromises = uploadedFiles.map((file, index) => {
              return new Promise((res, rej) => {
                try {
                  const isPrimary = !hasPrimaryImage && index === 0 ? 1 : 0; // 如果沒有主圖，第一張為主圖
                  const displayOrder = startOrder + index;
                  
                  // 生成正確的檔名
                  const sku = req.body.sku || req.query.sku;
                  const ext = path.extname(file.filename) || '.jpg';
                  let newFilename;
                  
                  if (isPrimary) {
                    // 主圖直接使用 SKU 名稱
                    newFilename = generateFilename(sku) + ext;
                  } else {
                    // 非主圖使用 SKU + 序號
                    const imageNumber = existingNonPrimaryCount + (index - (hasPrimaryImage ? 0 : 1)) + 1;
                    newFilename = generateFilename(sku) + '_' + imageNumber + ext;
                  }
                  
                  // 處理非法字元
                  const safeNewFilename = newFilename.replace(/[^a-zA-Z0-9.-_]/g, '_');
                  
                  // 重命名檔案
                  const oldPath = path.join(__dirname, '..', '..', 'public', 'uploads', 'products', file.filename);
                  const newPath = path.join(__dirname, '..', '..', 'public', 'uploads', 'products', safeNewFilename);
                  
                  // 如果目標檔案已存在，先刪除
                  if (fs.existsSync(newPath) && oldPath !== newPath) {
                    fs.unlinkSync(newPath);
                  }
                  
                  // 重命名檔案
                  if (fs.existsSync(oldPath)) {
                    fs.renameSync(oldPath, newPath);
                  }
                  
                  const imagePath = `/uploads/products/${safeNewFilename}`;

                  productDb.run(
                    'INSERT INTO product_image (product_id, image_path, is_primary, display_order) VALUES (?, ?, ?, ?)',
                    [productId, imagePath, isPrimary, displayOrder],
                    function(err) {
                      if (err) return rej(err);
                      res();
                    }
                  );
                } catch (error) {
                  rej(error);
                }
              });
            });

            await Promise.all(imagePromises);
            resolve();
          } catch (err) {
            reject(err);
          }
        }));
      }

      // 執行所有關聯更新
      try {
        await Promise.all(promises);

        // 獲取更新後的產品數據
        const updatedProduct = await new Promise((resolve, reject) => {
          productDb.get(
            `SELECT
              p.*,
              (SELECT image_path FROM product_image WHERE product_id = p.id AND is_primary = 1 LIMIT 1) as primary_image_path
             FROM product p WHERE p.id = ?`,
            [productId],
            (err, product) => {
              if (err) reject(err);
              else resolve(product || null);
            }
          );
        });

        if (!updatedProduct) {
          return res.status(404).json({ error: '更新後無法找到該產品' });
        }

        // 將 is_active 轉換為布林值
        if (updatedProduct) {
          updatedProduct.is_active = Boolean(updatedProduct.is_active);
        }

        res.status(200).json({
          message: `產品 ID ${productId} 更新成功`,
          product: updatedProduct
        });
      } catch (promiseErr) {
        logError(promiseErr, req, res, `更新產品 ${productId} 關聯數據時出錯`);
        return res.status(500).json({ error: `更新產品關聯數據時出錯: ${promiseErr.message}` });
      }
    } catch (err) {
      logError(err, req, res, `處理更新產品 ID ${req.params.id} 請求時發生意外錯誤`);
      res.status(500).json({ error: `伺服器內部錯誤: ${err.message}` });
    }
  });

  // DELETE /admin/products/:id - 刪除產品
  router.delete('/:id', authenticateAdmin, async (req, res) => {
    try {
      const productId = req.params.id;

      // 確保產品存在
      const product = await new Promise((resolve, reject) => {
        productDb.get('SELECT * FROM product WHERE id = ?', [productId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (!product) {
        return res.status(404).json({ error: '找不到要刪除的產品' });
      }

      // 開始事務
      await new Promise((resolve, reject) => {
        productDb.run('BEGIN TRANSACTION', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      try {
        // 1. 刪除商品圖片記錄
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM product_image WHERE product_id = ?', [productId], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        // 2. 刪除商品規格記錄
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM product_spec WHERE product_id = ?', [productId], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        // 3. 刪除商品分類關聯
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM product_category WHERE product_id = ?', [productId], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        // 4. 刪除商品價格記錄
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM product_price WHERE product_id = ?', [productId], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        // 5. 刪除庫存記錄
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM inventory WHERE product_id = ?', [productId], (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        // 6. 最後刪除主商品記錄
        await new Promise((resolve, reject) => {
          productDb.run('DELETE FROM product WHERE id = ?', [productId], function(err) {
            if (err) reject(err);
            else resolve(this.changes);
          });
        });

        // 提交事務
        await new Promise((resolve, reject) => {
          productDb.run('COMMIT', (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        res.status(200).json({ message: '產品已成功刪除' });

      } catch (error) {
        // 回滾事務
        await new Promise((resolve) => {
          productDb.run('ROLLBACK', () => resolve());
        });
        throw error;
      }

    } catch (err) {
      console.error('刪除產品失敗:', err);
      logError(err, req, res, `刪除產品 ${productId} 失敗`);
      res.status(500).json({ error: '刪除產品失敗' });
    }
  });

  // 新增：獲取產品規格
  router.get('/:id/specs', authenticateAdmin, (req, res) => {
    try {
      const productId = req.params.id;
      productDb.all(
        `SELECT * FROM product_spec WHERE product_id = ?`,
        [productId],
        (err, specs) => {
          if (err) {
            logError(err, req, res, `獲取產品ID ${productId} 的規格失敗`);
            return res.status(500).json({ error: '獲取產品規格失敗' });
          }

          // 將相同規格名稱的多個規格值合併為一個
          const specMap = {};
          specs.forEach(spec => {
            if (!specMap[spec.spec_name]) {
              specMap[spec.spec_name] = {...spec};
            } else if (spec.spec_name === '規格') {
              // 對於"規格"，將多個值用逗號連接
              specMap[spec.spec_name].spec_value = specMap[spec.spec_name].spec_value
                ? `${specMap[spec.spec_name].spec_value},${spec.spec_value}`
                : spec.spec_value;
            }
          });

          // 將結果轉換回數組
          const mergedSpecs = Object.values(specMap);

          res.json(mergedSpecs || []);
        }
      );
    } catch (err) {
      logError(err, req, res, '處理產品規格請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：獲取產品分類
  router.get('/:id/categories', authenticateAdmin, (req, res) => {
    try {
      const productId = req.params.id;
      productDb.all(
        `SELECT pc.*, c.name as category_name
         FROM product_category pc
         LEFT JOIN category c ON pc.category_id = c.id
         WHERE pc.product_id = ?`,
        [productId],
        (err, categories) => {
          if (err) {
            logError(err, req, res, `獲取產品ID ${productId} 的分類失敗`);
            return res.status(500).json({ error: '獲取產品分類失敗' });
          }
          res.json(categories || []);
        }
      );
    } catch (err) {
      logError(err, req, res, '處理產品分類請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：獲取產品價格
  router.get('/:id/prices', authenticateAdmin, (req, res) => {
    try {
      const productId = req.params.id;
      productDb.all(
        `SELECT * FROM product_price WHERE product_id = ?`,
        [productId],
        (err, prices) => {
          if (err) {
            logError(err, req, res, `獲取產品ID ${productId} 的價格失敗`);
            return res.status(500).json({ error: '獲取產品價格失敗' });
          }

          // 處理數值轉換
          const formattedPrices = prices.map(price => ({
            ...price,
            price: price.price !== null ? Number(price.price) : null
          }));

          res.json(formattedPrices || []);
        }
      );
    } catch (err) {
      logError(err, req, res, '處理產品價格請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：獲取產品圖片
  router.get('/:id/images', authenticateAdmin, (req, res) => {
    try {
      const productId = req.params.id;
      productDb.all(
        `SELECT * FROM product_image WHERE product_id = ? ORDER BY is_primary DESC, display_order ASC`,
        [productId],
        (err, images) => {
          if (err) {
            logError(err, req, res, `獲取產品ID ${productId} 的圖片失敗`);
            return res.status(500).json({ error: '獲取產品圖片失敗' });
          }

          // 處理布林值轉換
          const formattedImages = images.map(image => ({
            ...image,
            is_primary: Boolean(image.is_primary)
          }));

          res.json(formattedImages || []);
        }
      );
    } catch (err) {
      logError(err, req, res, '處理產品圖片請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // --- 新增：根據SKU查詢商品 ---
  router.get('/sku/:sku', authenticateAdmin, (req, res) => {
    try {
      const { sku } = req.params;

      if (!sku) {
        return res.status(400).json({ error: '請提供商品 SKU' });
      }

      // 查詢商品基本信息、分類和圖片
      const sql = `
        SELECT
          p.id,
          p.name,
          p.sku,
          p.specs_text as specification,
          pp.price as cost,
          c.name as category_name,
          pi.image_path as primary_image_path,
          (SELECT COUNT(*) FROM inventory i WHERE i.product_id = p.id AND i.warehouse_id = 1) as has_stock,
          (SELECT quantity FROM inventory i WHERE i.product_id = p.id AND i.warehouse_id = 1 LIMIT 1) as current_stock
        FROM product p
        LEFT JOIN product_category pc ON p.id = pc.product_id
        LEFT JOIN category c ON pc.category_id = c.id
        LEFT JOIN product_image pi ON p.id = pi.product_id AND pi.is_primary = 1
        LEFT JOIN product_price pp ON p.id = pp.product_id AND pp.price_type_id = 1
        WHERE p.sku = ? AND p.is_active = 1
        LIMIT 1
      `;

      productDb.get(sql, [sku], (err, product) => {
        if (err) {
          logError(err, req, res, `根據SKU ${sku} 查詢商品失敗`);
          return res.status(500).json({ error: '查詢商品失敗' });
        }

        if (!product) {
          return res.status(404).json({ error: '找不到該商品' });
        }

        // 確保 SKU 沒有前後空白字符
        if (product.sku) {
          product.sku = product.sku.trim();
        }

        // 處理庫存信息
        product.current_stock = product.current_stock || 0;
        product.has_stock = product.has_stock > 0;

        res.json(product);
      });
    } catch (err) {
      logError(err, req, res, '處理根據SKU查詢商品請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：刪除產品圖片
  router.delete('/:id/images/:imageId', authenticateAdmin, (req, res) => {
    try {
      const { id: productId, imageId } = req.params;

      // 先檢查圖片是否存在並獲取圖片資訊
      productDb.get(
        'SELECT * FROM product_image WHERE id = ? AND product_id = ?',
        [imageId, productId],
        (err, image) => {
          if (err) {
            logError(err, req, res, `檢查圖片 ${imageId} 時出錯`);
            return res.status(500).json({ error: '檢查圖片時發生錯誤' });
          }

          if (!image) {
            return res.status(404).json({ error: '找不到要刪除的圖片' });
          }

          // 刪除圖片記錄
          productDb.run(
            'DELETE FROM product_image WHERE id = ?',
            [imageId],
            function(err) {
              if (err) {
                logError(err, req, res, `刪除圖片 ${imageId} 時出錯`);
                return res.status(500).json({ error: '刪除圖片時發生錯誤' });
              }

              // 如果是主圖片，需要設置新的主圖片
              if (image.is_primary) {
                productDb.get(
                  'SELECT id FROM product_image WHERE product_id = ? ORDER BY display_order ASC LIMIT 1',
                  [productId],
                  (err, newPrimaryImage) => {
                    if (err || !newPrimaryImage) {
                      // 如果沒有其他圖片或發生錯誤，直接返回成功
                      return res.status(200).json({ message: '圖片已成功刪除' });
                    }

                    // 設置新的主圖片
                    productDb.run(
                      'UPDATE product_image SET is_primary = 1 WHERE id = ?',
                      [newPrimaryImage.id],
                      (err) => {
                        if (err) {
                          logError(err, req, res, `更新新主圖片 ${newPrimaryImage.id} 時出錯`);
                        }
                        res.status(200).json({ message: '圖片已成功刪除並更新主圖片' });
                      }
                    );
                  }
                );
              } else {
                res.status(200).json({ message: '圖片已成功刪除' });
              }

              // 嘗試從文件系統中刪除實際圖片文件
              try {
                const imagePath = path.join(__dirname, '..', '..', 'public', image.image_path);
                if (fs.existsSync(imagePath)) {
                  fs.unlinkSync(imagePath);
                }
              } catch (fsErr) {
                // 僅記錄錯誤但不影響API響應
                console.error(`刪除圖片文件 ${image.image_path} 時出錯:`, fsErr);
              }
            }
          );
        }
      );
    } catch (err) {
      logError(err, req, res, '處理刪除圖片請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：設定產品主圖
  router.put('/:id/images/:imageId/primary', authenticateAdmin, (req, res) => {
    try {
      const { id: productId, imageId } = req.params;

      // 先檢查圖片是否存在
      productDb.get(
        'SELECT * FROM product_image WHERE id = ? AND product_id = ?',
        [imageId, productId],
        (err, image) => {
          if (err) {
            logError(err, req, res, `檢查圖片 ${imageId} 時出錯`);
            return res.status(500).json({ error: '檢查圖片時發生錯誤' });
          }

          if (!image) {
            return res.status(404).json({ error: '找不到要設為主圖的圖片' });
          }

          // 已經是主圖則無需更改
          if (image.is_primary) {
            return res.status(200).json({ message: '此圖片已經是主圖', success: true });
          }

          // 開始一個事務，確保更新操作的一致性
          productDb.run('BEGIN TRANSACTION', (beginErr) => {
            if (beginErr) {
              logError(beginErr, req, res, '開始事務失敗');
              return res.status(500).json({ error: '設定主圖失敗' });
            }

            // 先將所有圖片設為非主圖
            productDb.run(
              'UPDATE product_image SET is_primary = 0 WHERE product_id = ?',
              [productId],
              (resetErr) => {
                if (resetErr) {
                  productDb.run('ROLLBACK');
                  logError(resetErr, req, res, '重設主圖狀態失敗');
                  return res.status(500).json({ error: '設定主圖失敗' });
                }

                // 將目標圖片設為主圖
                productDb.run(
                  'UPDATE product_image SET is_primary = 1 WHERE id = ?',
                  [imageId],
                  (updateErr) => {
                    if (updateErr) {
                      productDb.run('ROLLBACK');
                      logError(updateErr, req, res, `設定圖片 ${imageId} 為主圖失敗`);
                      return res.status(500).json({ error: '設定主圖失敗' });
                    }

                    // 提交事務
                    productDb.run('COMMIT', (commitErr) => {
                      if (commitErr) {
                        productDb.run('ROLLBACK');
                        logError(commitErr, req, res, '提交事務失敗');
                        return res.status(500).json({ error: '設定主圖失敗' });
                      }

                      res.status(200).json({
                        message: '已成功設定主圖',
                        success: true
                      });
                    });
                  }
                );
              }
            );
          });
        }
      );
    } catch (err) {
      logError(err, req, res, '處理設定主圖請求時出錯');
      res.status(500).json({ error: '伺服器內部錯誤' });
    }
  });

  // 新增：複製商品
  router.post('/:id/copy', authenticateAdmin, async (req, res) => {
    try {
      const originalProductId = req.params.id;

      // 獲取原商品的完整信息
      const originalProduct = await new Promise((resolve, reject) => {
        const sql = `
          SELECT
            p.*,
            pc.category_id,
            c.name as category_name
          FROM product p
          LEFT JOIN product_category pc ON p.id = pc.product_id
          LEFT JOIN category c ON pc.category_id = c.id
          WHERE p.id = ?
        `;

        productDb.get(sql, [originalProductId], (err, row) => {
          if (err) reject(err);
          else resolve(row);
        });
      });

      if (!originalProduct) {
        return res.status(404).json({ error: '找不到要複製的商品' });
      }

      // 生成新的SKU
      const newSku = await generateNewSku(originalProduct.sku, originalProduct.category_id);

      // 獲取新的排序值
      const newSortOrder = await getNextSortOrder();

      // 開始事務
      await new Promise((resolve, reject) => {
        productDb.run('BEGIN TRANSACTION', (err) => {
          if (err) reject(err);
          else resolve();
        });
      });

      try {
        // 插入新商品，繼承原商品的排序值
        const newProductId = await new Promise((resolve, reject) => {
          const insertSql = `
            INSERT INTO product (
              name, sku, description, brand_id, is_active,
              stock_alert_threshold, specs_text, specs_json, sort_order
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          productDb.run(insertSql, [
            originalProduct.name,
            newSku,
            originalProduct.description || '',
            originalProduct.brand_id,
            0, // 預設為不啟用
            originalProduct.stock_alert_threshold || 10,
            originalProduct.specs_text,
            originalProduct.specs_json,
            originalProduct.sort_order || 0 // 繼承原商品的排序值
          ], function(err) {
            if (err) reject(err);
            else resolve(this.lastID);
          });
        });

        // 複製商品分類關聯
        if (originalProduct.category_id) {
          await new Promise((resolve, reject) => {
            productDb.run(
              'INSERT INTO product_category (product_id, category_id) VALUES (?, ?)',
              [newProductId, originalProduct.category_id],
              (err) => {
                if (err) reject(err);
                else resolve();
              }
            );
          });
        }

        // 複製商品規格
        await new Promise((resolve, reject) => {
          productDb.all(
            'SELECT * FROM product_spec WHERE product_id = ?',
            [originalProductId],
            (err, specs) => {
              if (err) {
                reject(err);
                return;
              }

              if (specs.length === 0) {
                resolve();
                return;
              }

              const promises = specs.map(spec => {
                return new Promise((specResolve, specReject) => {
                  productDb.run(
                    'INSERT INTO product_spec (product_id, spec_name, spec_value) VALUES (?, ?, ?)',
                    [newProductId, spec.spec_name, spec.spec_value],
                    (specErr) => {
                      if (specErr) specReject(specErr);
                      else specResolve();
                    }
                  );
                });
              });

              Promise.all(promises).then(resolve).catch(reject);
            }
          );
        });

        // 不設置預設零售價，讓用戶自行填入
        // 複製商品時零售價預設為空

        // 設置預設庫存（0）
        await new Promise((resolve, reject) => {
          // 先查詢是否有預設倉庫（ID為1的台北倉）
          productDb.get('SELECT id FROM warehouse WHERE id = 1', [], (err, warehouse) => {
            if (err) {
              reject(err);
              return;
            }

            const warehouseId = warehouse ? 1 : null;
            if (warehouseId) {
              productDb.run(
                'INSERT INTO inventory (product_id, warehouse_id, quantity) VALUES (?, ?, 0)',
                [newProductId, warehouseId],
                (invErr) => {
                  if (invErr) reject(invErr);
                  else resolve();
                }
              );
            } else {
              resolve(); // 如果沒有倉庫，跳過庫存設置
            }
          });
        });

        // 提交事務
        await new Promise((resolve, reject) => {
          productDb.run('COMMIT', (err) => {
            if (err) reject(err);
            else resolve();
          });
        });

        res.status(201).json({
          message: '商品複製成功',
          newProductId: newProductId,
          newSku: newSku
        });

      } catch (error) {
        // 回滾事務
        await new Promise((resolve) => {
          productDb.run('ROLLBACK', () => resolve());
        });
        throw error;
      }

    } catch (error) {
      console.error('複製商品失敗:', error);
      logError(error, req, res, '複製商品失敗');
      res.status(500).json({ error: '複製商品失敗' });
    }
  });

  // 生成新SKU的輔助函數 - 實時計算，確保連續性
  async function generateNewSku(originalSku, categoryId) {
    if (!originalSku) {
      throw new Error('原商品SKU不存在');
    }

    // 解析原SKU格式，例如：MI-DOC-X00-32
    const skuParts = originalSku.split('-');
    if (skuParts.length < 4) {
      // 如果SKU格式不符合預期，直接在後面加數字
      return await findNextAvailableSku(originalSku);
    }

    const prefix = skuParts.slice(0, -1).join('-'); // MI-DOC-X00
    const currentNumber = parseInt(skuParts[skuParts.length - 1]); // 32

    if (isNaN(currentNumber)) {
      return await findNextAvailableSku(originalSku);
    }

    // 實時查詢該分類下相同前綴的最大編號
    return new Promise((resolve, reject) => {
      const sql = `
        SELECT p.sku
        FROM product p
        LEFT JOIN product_category pc ON p.id = pc.product_id
        WHERE p.sku LIKE ? AND (pc.category_id = ? OR ? IS NULL)
        ORDER BY p.sku
      `;

      productDb.all(sql, [`${prefix}-%`, categoryId, categoryId], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        // 收集所有現有的數字編號
        const existingNumbers = new Set();
        rows.forEach(row => {
          const parts = row.sku.split('-');
          const num = parseInt(parts[parts.length - 1]);
          if (!isNaN(num)) {
            existingNumbers.add(num);
          }
        });

        // 從原編號+1開始，找到第一個未使用的編號
        let nextNumber = currentNumber + 1;
        while (existingNumbers.has(nextNumber)) {
          nextNumber++;
        }

        const newSku = `${prefix}-${nextNumber}`;

        // 最終檢查SKU是否真的可用（防止併發問題）
        productDb.get('SELECT id FROM product WHERE sku = ?', [newSku], (checkErr, existing) => {
          if (checkErr) {
            reject(checkErr);
            return;
          }

          if (existing) {
            // 如果仍然衝突，遞歸重新生成
            generateNewSku(originalSku, categoryId).then(resolve).catch(reject);
          } else {
            resolve(newSku);
          }
        });
      });
    });
  }

  // 查找下一個可用SKU的輔助函數 - 實時計算，確保連續性
  async function findNextAvailableSku(baseSku) {
    return new Promise((resolve, reject) => {
      // 查詢所有以baseSku開頭的SKU
      const sql = 'SELECT sku FROM product WHERE sku LIKE ? ORDER BY sku';

      productDb.all(sql, [`${baseSku}-%`], (err, rows) => {
        if (err) {
          reject(err);
          return;
        }

        // 收集所有現有的數字後綴
        const existingNumbers = new Set();
        rows.forEach(row => {
          const suffix = row.sku.replace(`${baseSku}-`, '');
          const num = parseInt(suffix);
          if (!isNaN(num)) {
            existingNumbers.add(num);
          }
        });

        // 從1開始找到第一個未使用的數字
        let counter = 1;
        while (existingNumbers.has(counter)) {
          counter++;
        }

        const newSku = `${baseSku}-${counter}`;

        // 最終檢查SKU是否真的可用（防止併發問題）
        productDb.get('SELECT id FROM product WHERE sku = ?', [newSku], (checkErr, existing) => {
          if (checkErr) {
            reject(checkErr);
            return;
          }

          if (existing) {
            // 如果仍然衝突，遞歸重新生成
            findNextAvailableSku(baseSku).then(resolve).catch(reject);
          } else {
            resolve(newSku);
          }
        });
      });
    });
  }

  // 獲取下一個排序值的輔助函數
  async function getNextSortOrder() {
    return new Promise((resolve, reject) => {
      productDb.get(
        'SELECT MAX(sort_order) as max_sort_order FROM product',
        [],
        (err, row) => {
          if (err) {
            reject(err);
            return;
          }

          const maxSortOrder = row && row.max_sort_order ? parseInt(row.max_sort_order) : 0;
          resolve(maxSortOrder + 1);
        }
      );
    });
  }



  // PUT /admin/products/:id/sort - 更新單個商品排序
  router.put('/:id/sort', authenticateAdmin, async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const { sort_order } = req.body;

      if (!productId || typeof sort_order !== 'number') {
        return res.status(400).json({ error: '商品 ID 或排序值格式錯誤' });
      }

      const result = await new Promise((resolve, reject) => {
        productDb.run(
          'UPDATE product SET sort_order = ?, updated_at = datetime("now", "+8 hours") WHERE id = ?',
          [sort_order, productId],
          function(err) {
            if (err) {
              reject(err);
            } else {
              resolve(this.changes);
            }
          }
        );
      });

      if (result === 0) {
        return res.status(404).json({ error: '找不到該商品' });
      }

      res.json({ message: '商品排序更新成功' });

    } catch (error) {
      console.error('更新商品排序失敗:', error);
      logError(error, req, res, '更新商品排序失敗');
      res.status(500).json({ error: '更新商品排序失敗' });
    }
  });



  // 根據 SKU 獲取商品信息 - 用於盤點管理
  router.get('/sku/:sku', authenticateAdmin, (req, res) => {
    const { sku } = req.params;

    const sql = `
      SELECT
        p.id,
        p.name,
        p.sku,
        p.specification,
        c.name as category_name,
        COALESCE(inv.quantity, 0) as current_stock,
        pi.image_path as primary_image_path
      FROM product p
      LEFT JOIN product_category pc ON p.id = pc.product_id
      LEFT JOIN category c ON pc.category_id = c.id
      LEFT JOIN (
        SELECT product_id, SUM(quantity) as quantity
        FROM inventory
        GROUP BY product_id
      ) AS inv ON p.id = inv.product_id
      LEFT JOIN (
        SELECT product_id, image_path
        FROM product_image
        WHERE is_primary = 1
        LIMIT 1
      ) AS pi ON p.id = pi.product_id
      WHERE p.sku = ? AND p.is_active = 1
    `;

    productDb.get(sql, [sku], (err, result) => {
      if (err) {
        console.error('根據SKU獲取商品失敗:', err);
        logError(err, req, res, '根據SKU獲取商品失敗');
        return res.status(500).json({
          success: false,
          message: '根據SKU獲取商品失敗'
        });
      }

      if (!result) {
        return res.status(404).json({
          success: false,
          message: '找不到該商品'
        });
      }

      res.json({
        success: true,
        data: result
      });
    });
  });

  return router;
};